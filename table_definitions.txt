股票财务数据库表定义

=== daily_basic 表 ===
股票代码	symbol	varchar	20
交易日期	trade_date	varchar	20
H股非限售股数	h_shr_unl	double	-
A股非限售股数	a_shr_unl	double	-
总股本	ttl_shr	double	-
限售总股本	ttl_shr_ltd	double	-
换手率	turnrate	double	-
非限售总股本	ttl_shr_unl	double	-
流通股本	circ_shr	double	-
收盘价	tclose	double	-
创建时间	create_time	datetime	-

=== daily_mktvalue 表 ===
股票代码	symbol	varchar	20
交易日期	trade_date	varchar	20
总市值(证监会)	tot_mv_csrc	double	-
企业价值	ev	double	-
股权价值	equity_value	double	-
A股流通市值(不含限售)	a_mv_ex_ltd	double	-
总市值	tot_mv	double	-
企业倍数	ev_ebitda	double	-
A股流通市值	a_mv	double	-
B股流通市值(不含限售)	b_mv_ex_ltd	double	-
B股流通市值	b_mv	double	-
企业价值(剔除货币资金)	ev_ex_curr	double	-
创建时间	create_time	datetime	-

=== daily_valuation 表 ===
股票代码	symbol	varchar	20
交易日期	trade_date	varchar	20
市盈率TTM	pe_ttm	double	-
市销率TTM	ps_ttm	double	-
股息率TTM	dy_ttm	double	-
股息率LFY	dy_lfy	double	-
市销率MRQ	ps_mrq	double	-
PEG值MRQ	peg_mrq	double	-
市净率LYR	pb_lyr	double	-
市盈率TTM(扣除)	pe_ttm_cut	double	-
市销率LYR	ps_lyr	double	-
市净率MRQ	pb_mrq	double	-
创建时间	create_time	datetime	-

=== finance_deriv 表 ===
股票代码	symbol	varchar	20
发布日期	pub_date	varchar	20
报告日期	rpt_date	varchar	20
报表类型	rpt_type	bigint	-
数据类型	data_type	bigint	-
销售毛利率	sale_gpm	double	-
总资产报酬率	roa	double	-
净资产收益率	roe	double	-
净资产收益率(扣除)	roe_cut	double	-
资产负债率	ast_liab_rate	double	-
销售净利率	sale_npm	double	-
创建时间	create_time	datetime	-

=== fundamentals_income 表 ===
股票代码	symbol	varchar	20
发布日期	pub_date	varchar	20
报告日期	rpt_date	varchar	20
营业收入	inc_oper	double	-
报表类型	rpt_type	bigint	-
数据类型	data_type	bigint	-
净利润	net_prof	double	-
创建时间	create_time	datetime	-

数据库索引信息：
- daily_basic: PRIMARY KEY (symbol, trade_date), INDEX idx_trade_date_turnrate (trade_date, turnrate)
- daily_mktvalue: PRIMARY KEY (symbol, trade_date), INDEX idx_tot_mv_trade_date (tot_mv, trade_date)  
- daily_valuation: PRIMARY KEY (symbol, trade_date), INDEX idx_trade_date_pe_dy (trade_date, pe_ttm, dy_ttm)
- finance_deriv: PRIMARY KEY (symbol, rpt_date)
- fundamentals_income: PRIMARY KEY (symbol, rpt_date)

数据库引擎：InnoDB
字符集：utf8mb4
排序规则：utf8mb4_0900_ai_ci
