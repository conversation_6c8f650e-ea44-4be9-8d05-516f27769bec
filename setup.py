"""
股票回测系统安装和设置脚本
"""

import os
import sys
import subprocess

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    dependencies = [
        'mysql-connector-python>=8.0.0'
    ]
    
    for dep in dependencies:
        try:
            print(f"安装 {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✓ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {dep} 安装失败")
            return False
    
    return True

def create_config_template():
    """创建配置文件模板"""
    config_content = '''"""
回测系统配置文件
请根据实际情况修改以下配置
"""

# 数据库连接配置
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'root',            # 数据库用户名
    'password': 'your_password',  # 数据库密码，请修改为实际密码
    'database': 'stock_goldminer_bj',  # 数据库名称
    'charset': 'utf8mb4'       # 字符集
}

# 回测策略参数
STRATEGY_CONFIG = {
    'initial_capital': 1000000,    # 初始资金（元）
    'rebalance_freq': 20,          # 调仓频率（交易日）
    'max_positions': 30,           # 最大持仓数量
    'pe_min': 0,                   # 市盈率最小值
    'pe_max': 40,                  # 市盈率最大值
    'cash_reserve_ratio': 0.02     # 现金保留比例（2%）
}

# 回测时间配置
TIME_CONFIG = {
    'start_date': '2024-01-01',    # 回测开始日期
    'end_date': None               # 回测结束日期，None表示到今天
}

# 输出配置
OUTPUT_CONFIG = {
    'save_csv': True,              # 是否保存CSV文件
    'csv_filename': 'backtest_results.csv',  # CSV文件名
    'show_progress': True,         # 是否显示进度
    'progress_interval': 10        # 进度显示间隔（天）
}
'''
    
    if not os.path.exists('config.py'):
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✓ 配置文件 config.py 已创建")
    else:
        print("✓ 配置文件 config.py 已存在")

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        'basic_backtest.py',
        'test_database.py',
        'stock_goldminer_bj.sql',
        'table_definitions.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✓ {file} 存在")
    
    if missing_files:
        print(f"✗ 缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("安装完成！后续步骤:")
    print("=" * 60)
    print("1. 设置数据库:")
    print("   - 创建MySQL数据库: stock_goldminer_bj")
    print("   - 导入表结构: mysql -u root -p stock_goldminer_bj < stock_goldminer_bj.sql")
    print("   - 导入股票数据（需要自行准备）")
    print()
    print("2. 修改配置:")
    print("   - 编辑 config.py 文件")
    print("   - 设置正确的数据库连接参数")
    print("   - 根据需要调整策略参数")
    print()
    print("3. 测试连接:")
    print("   python test_database.py")
    print()
    print("4. 运行回测:")
    print("   python basic_backtest.py")
    print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("股票回测系统安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("✗ 需要Python 3.6或更高版本")
        return
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查文件
    print("\n检查必要文件...")
    if not check_files():
        print("✗ 缺少必要文件，请确保所有文件都在当前目录")
        return
    
    # 安装依赖
    print("\n安装依赖包...")
    if not install_dependencies():
        print("✗ 依赖包安装失败")
        return
    
    # 创建配置文件
    print("\n创建配置文件...")
    create_config_template()
    
    # 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    main()
