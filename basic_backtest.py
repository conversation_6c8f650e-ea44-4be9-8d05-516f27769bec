"""
基础版股票回测策略 - 无外部依赖
筛选条件：市盈率0-40
排序条件：市值最小
调仓频率：每20个交易日
持仓数量：30只股票
"""

import random
import math
from datetime import datetime, timedelta

def generate_sample_data(start_date='2025-01-01', days=60):
    """生成示例股票数据"""
    print("正在生成示例数据...")
    
    # 生成100只股票
    symbols = [f"SH60{i:04d}" for i in range(1, 101)]
    
    data = []
    random.seed(42)  # 固定随机种子
    
    # 为每只股票生成基础属性
    stock_base_data = {}
    for symbol in symbols:
        stock_base_data[symbol] = {
            'base_pe': random.uniform(8, 35),
            'base_mv': random.uniform(5e8, 5e11),  # 5亿到5000亿
            'base_price': random.uniform(10, 100),
            'volatility': random.uniform(0.15, 0.4)
        }
    
    # 生成日期数据
    base_date = datetime.strptime(start_date, '%Y-%m-%d')
    
    for day in range(days):
        # 跳过周末
        current_date = base_date + timedelta(days=day)
        if current_date.weekday() >= 5:  # 周六日
            continue
            
        for symbol in symbols:
            base_data = stock_base_data[symbol]
            
            # 添加随机波动
            price_change = random.gauss(0, base_data['volatility'] / math.sqrt(252))
            current_price = base_data['base_price'] * (1 + price_change * (day + 1) * 0.1)
            current_price = max(current_price, 1)  # 价格不能为负
            
            # 市盈率有一定波动
            pe_ttm = base_data['base_pe'] * random.uniform(0.8, 1.2)
            pe_ttm = max(pe_ttm, 1)  # 确保PE为正
            
            # 市值随股价变动
            tot_mv = base_data['base_mv'] * (current_price / base_data['base_price'])
            
            # 只保留PE在0-40范围内的股票
            if 0 < pe_ttm <= 40:
                data.append({
                    'symbol': symbol,
                    'trade_date': current_date.strftime('%Y-%m-%d'),
                    'pe_ttm': pe_ttm,
                    'tot_mv': tot_mv,
                    'close_price': current_price,
                    'turnrate': random.uniform(0.5, 8.0)
                })
    
    print(f"生成了 {len(data)} 条数据记录")
    return data

class SimpleBacktest:
    """简单回测类"""
    
    def __init__(self, initial_capital=1000000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}  # {symbol: shares}
        self.portfolio_values = []
        self.rebalance_freq = 20  # 每20个交易日调仓
        self.max_positions = 30   # 最大持仓30只
        
    def group_by_date(self, data):
        """按日期分组数据"""
        grouped = {}
        for record in data:
            date = record['trade_date']
            if date not in grouped:
                grouped[date] = []
            grouped[date].append(record)
        return grouped
    
    def run_backtest(self, data):
        """运行回测"""
        # 按日期分组
        daily_data = self.group_by_date(data)
        dates = sorted(daily_data.keys())
        
        print(f"回测期间: {dates[0]} 到 {dates[-1]}")
        print(f"总交易日数: {len(dates)}")
        
        rebalance_dates = dates[::self.rebalance_freq]  # 每20天调仓
        print(f"调仓次数: {len(rebalance_dates)}")
        
        for i, date in enumerate(dates):
            day_data = daily_data[date]
            
            # 检查是否为调仓日
            if date in rebalance_dates:
                self.rebalance(day_data, date)
            
            # 计算当日组合价值
            portfolio_value = self.calculate_portfolio_value(day_data)
            
            self.portfolio_values.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'cash': self.cash,
                'stock_value': portfolio_value - self.cash,
                'positions_count': len([p for p in self.positions.values() if p > 0])
            })
            
            if i % 10 == 0:  # 每10天打印一次进度
                print(f"进度: {i+1}/{len(dates)}, 组合价值: {portfolio_value:,.2f}")
        
        return self.portfolio_values
    
    def rebalance(self, day_data, date):
        """执行调仓"""
        print(f"\n调仓日期: {date}")
        
        # 卖出所有持仓
        total_stock_value = 0
        for symbol, shares in self.positions.items():
            if shares > 0:
                # 查找股票当日价格
                for stock in day_data:
                    if stock['symbol'] == symbol:
                        price = stock['close_price']
                        total_stock_value += shares * price
                        break
        
        self.cash += total_stock_value
        self.positions = {}
        
        # 按市值排序，选择最小的30只
        day_data.sort(key=lambda x: x['tot_mv'])
        selected_stocks = day_data[:self.max_positions]
        
        if len(selected_stocks) == 0:
            print("没有符合条件的股票")
            return
        
        # 等权重分配资金
        cash_per_stock = self.cash * 0.98 / len(selected_stocks)  # 保留2%现金
        
        successful_purchases = 0
        for stock in selected_stocks:
            symbol = stock['symbol']
            price = stock['close_price']
            shares = int(cash_per_stock / price)
            
            if shares > 0:
                cost = shares * price
                self.positions[symbol] = shares
                self.cash -= cost
                successful_purchases += 1
        
        print(f"成功买入 {successful_purchases} 只股票")
        print(f"剩余现金: {self.cash:,.2f}")
    
    def calculate_portfolio_value(self, day_data):
        """计算投资组合价值"""
        total_value = self.cash
        
        # 创建价格字典便于查找
        price_dict = {stock['symbol']: stock['close_price'] for stock in day_data}
        
        for symbol, shares in self.positions.items():
            if shares > 0 and symbol in price_dict:
                total_value += shares * price_dict[symbol]
        
        return total_value

def calculate_performance_metrics(portfolio_values):
    """计算回测表现指标"""
    if not portfolio_values:
        return None
    
    # 计算日收益率
    daily_returns = []
    for i in range(1, len(portfolio_values)):
        prev_value = portfolio_values[i-1]['portfolio_value']
        curr_value = portfolio_values[i]['portfolio_value']
        daily_return = (curr_value / prev_value - 1) if prev_value > 0 else 0
        daily_returns.append(daily_return)
    
    # 基本指标
    start_value = portfolio_values[0]['portfolio_value']
    end_value = portfolio_values[-1]['portfolio_value']
    total_return = (end_value / start_value - 1) * 100
    
    # 年化收益率
    days = len(portfolio_values)
    annual_return = ((end_value / start_value) ** (252 / days) - 1) * 100
    
    # 最大回撤
    max_value = start_value
    max_drawdown = 0
    for pv in portfolio_values:
        current_value = pv['portfolio_value']
        if current_value > max_value:
            max_value = current_value
        drawdown = (current_value / max_value - 1) * 100
        if drawdown < max_drawdown:
            max_drawdown = drawdown
    
    # 波动率
    if len(daily_returns) > 1:
        mean_return = sum(daily_returns) / len(daily_returns)
        variance = sum((r - mean_return) ** 2 for r in daily_returns) / (len(daily_returns) - 1)
        volatility = math.sqrt(variance * 252) * 100
    else:
        volatility = 0
    
    # 夏普比率（假设无风险利率3%）
    risk_free_rate = 0.03
    excess_return = annual_return / 100 - risk_free_rate
    sharpe_ratio = excess_return / (volatility / 100) if volatility > 0 else 0
    
    # 胜率
    win_count = sum(1 for r in daily_returns if r > 0)
    win_rate = (win_count / len(daily_returns) * 100) if daily_returns else 0
    
    return {
        'start_value': start_value,
        'end_value': end_value,
        'total_return': total_return,
        'annual_return': annual_return,
        'max_drawdown': max_drawdown,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate,
        'trading_days': days
    }

def save_results_to_csv(portfolio_values, filename='backtest_results.csv'):
    """保存结果到CSV文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write('date,portfolio_value,cash,stock_value,positions_count\n')
            
            # 写入数据
            for pv in portfolio_values:
                f.write(f"{pv['date']},{pv['portfolio_value']:.2f},{pv['cash']:.2f},"
                       f"{pv['stock_value']:.2f},{pv['positions_count']}\n")
        
        print(f"结果已保存到 {filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("小市值低市盈率策略回测")
    print("=" * 60)
    
    # 生成示例数据（60个交易日，约3个月）
    data = generate_sample_data('2025-01-01', days=90)
    
    if not data:
        print("没有数据，退出程序")
        return
    
    # 运行回测
    strategy = SimpleBacktest(initial_capital=1000000)
    portfolio_values = strategy.run_backtest(data)
    
    # 计算表现指标
    metrics = calculate_performance_metrics(portfolio_values)
    
    if metrics:
        print("\n" + "=" * 60)
        print("回测结果:")
        print("=" * 60)
        print(f"初始资金: ¥{metrics['start_value']:,.2f}")
        print(f"最终资金: ¥{metrics['end_value']:,.2f}")
        print(f"总收益率: {metrics['total_return']:.2f}%")
        print(f"年化收益率: {metrics['annual_return']:.2f}%")
        print(f"最大回撤: {metrics['max_drawdown']:.2f}%")
        print(f"年化波动率: {metrics['volatility']:.2f}%")
        print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
        print(f"胜率: {metrics['win_rate']:.2f}%")
        print(f"交易天数: {metrics['trading_days']}")
        print("=" * 60)
        
        # 保存结果
        save_results_to_csv(portfolio_values)
        
        # 显示最后几天的组合状态
        print("\n最后5天的组合状态:")
        print("日期\t\t组合价值\t现金\t\t股票价值\t持仓数")
        print("-" * 60)
        for pv in portfolio_values[-5:]:
            print(f"{pv['date']}\t{pv['portfolio_value']:,.0f}\t\t{pv['cash']:,.0f}\t\t"
                  f"{pv['stock_value']:,.0f}\t\t{pv['positions_count']}")
    
    else:
        print("计算表现指标失败")

if __name__ == "__main__":
    main()
