"""
基于MySQL数据库的股票回测策略
筛选条件：市盈率0-40
排序条件：市值最小
调仓频率：每20个交易日
持仓数量：30只股票
"""

import math
from datetime import datetime

# 导入配置
try:
    from config import DATABASE_CONFIG, STRATEGY_CONFIG, TIME_CONFIG, OUTPUT_CONFIG
except ImportError:
    print("警告: 未找到config.py文件，使用默认配置")
    DATABASE_CONFIG = {
        'host': 'localhost',
        'user': 'root',
        'password': '6547899x',
        'database': 'stock_goldminer_bj',
        'charset': 'utf8mb4'
    }
    STRATEGY_CONFIG = {
        'initial_capital': 1000000,
        'rebalance_freq': 20,
        'max_positions': 30,
        'pe_min': 0,
        'pe_max': 40,
        'cash_reserve_ratio': 0.02
    }
    TIME_CONFIG = {
        'start_date': '2025-01-01',
        'end_date': None
    }
    OUTPUT_CONFIG = {
        'save_csv': True,
        'csv_filename': 'backtest_results.csv',
        'show_progress': True,
        'progress_interval': 10
    }

class DatabaseConnector:
    """数据库连接器"""

    def __init__(self, config):
        self.config = config
        self.connection = None

    def connect(self):
        """连接数据库"""
        try:
            import mysql.connector
            self.connection = mysql.connector.connect(**self.config)
            print("数据库连接成功")
            return True
        except ImportError:
            print("错误: 请安装mysql-connector-python")
            print("运行: pip install mysql-connector-python")
            return False
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False

    def execute_query(self, query, params=None):
        """执行查询"""
        if not self.connection:
            if not self.connect():
                return None

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            result = cursor.fetchall()
            cursor.close()
            return result
        except Exception as e:
            print(f"查询执行失败: {e}")
            return None

    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()

def load_stock_data(db_connector, start_date='2025-01-01', end_date=None):
    """从数据库加载股票数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')

    print(f"正在从数据库加载数据: {start_date} 到 {end_date}")

    # 联合查询获取所需数据
    query = """
    SELECT
        dv.symbol,
        dv.trade_date,
        dv.pe_ttm,
        dm.tot_mv,
        db.tclose as close_price,
        db.turnrate
    FROM daily_valuation dv
    INNER JOIN daily_mktvalue dm ON dv.symbol = dm.symbol AND dv.trade_date = dm.trade_date
    INNER JOIN daily_basic db ON dv.symbol = db.symbol AND dv.trade_date = db.trade_date
    WHERE dv.trade_date >= %s
        AND dv.trade_date <= %s
        AND dv.pe_ttm > %s
        AND dv.pe_ttm <= %s
        AND dm.tot_mv IS NOT NULL
        AND db.tclose IS NOT NULL
        AND db.tclose > 0
    ORDER BY dv.trade_date, dm.tot_mv
    """

    pe_min = STRATEGY_CONFIG['pe_min']
    pe_max = STRATEGY_CONFIG['pe_max']
    result = db_connector.execute_query(query, (start_date, end_date, pe_min, pe_max))

    if result:
        print(f"成功加载 {len(result)} 条数据记录")
        # 转换数据类型
        for record in result:
            record['pe_ttm'] = float(record['pe_ttm']) if record['pe_ttm'] else 0
            record['tot_mv'] = float(record['tot_mv']) if record['tot_mv'] else 0
            record['close_price'] = float(record['close_price']) if record['close_price'] else 0
            record['turnrate'] = float(record['turnrate']) if record['turnrate'] else 0
        return result
    else:
        print("数据加载失败或无数据")
        return []

class SimpleBacktest:
    """简单回测类"""

    def __init__(self, config=None):
        if config is None:
            config = STRATEGY_CONFIG

        self.initial_capital = config['initial_capital']
        self.cash = self.initial_capital
        self.positions = {}  # {symbol: shares}
        self.position_prices = {}  # {symbol: buy_price} 记录买入价格
        self.portfolio_values = []
        self.trading_records = []  # 记录所有交易详情
        self.rebalance_freq = config['rebalance_freq']  # 调仓频率
        self.max_positions = config['max_positions']    # 最大持仓数量
        self.cash_reserve_ratio = config['cash_reserve_ratio']  # 现金保留比例
        
    def group_by_date(self, data):
        """按日期分组数据"""
        grouped = {}
        for record in data:
            date = record['trade_date']
            if date not in grouped:
                grouped[date] = []
            grouped[date].append(record)
        return grouped
    
    def run_backtest(self, data):
        """运行回测"""
        # 按日期分组
        daily_data = self.group_by_date(data)
        dates = sorted(daily_data.keys())
        
        print(f"回测期间: {dates[0]} 到 {dates[-1]}")
        print(f"总交易日数: {len(dates)}")
        
        rebalance_dates = dates[::self.rebalance_freq]  # 每20天调仓
        print(f"调仓次数: {len(rebalance_dates)}")
        
        for i, date in enumerate(dates):
            day_data = daily_data[date]
            
            # 检查是否为调仓日
            if date in rebalance_dates:
                self.rebalance(day_data, date)
            
            # 计算当日组合价值
            portfolio_value = self.calculate_portfolio_value(day_data)
            
            self.portfolio_values.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'cash': self.cash,
                'stock_value': portfolio_value - self.cash,
                'positions_count': len([p for p in self.positions.values() if p > 0])
            })
            
            if i % 1 == 0:  # 每10天打印一次进度
                print(f"{date} 进度: {i+1}/{len(dates)}, 组合价值: {portfolio_value:,.2f}")
        
        return self.portfolio_values
    
    def rebalance(self, day_data, date):
        """执行调仓"""
        print(f"\n调仓日期: {date}")

        # 创建价格字典便于查找
        price_dict = {stock['symbol']: stock['close_price'] for stock in day_data}

        # 卖出所有持仓并记录卖出交易
        total_stock_value = 0
        sell_records = []
        unsold_positions = []  # 记录无法卖出的持仓

        for symbol, shares in self.positions.items():
            if shares > 0:
                if symbol in price_dict:
                    # 有价格数据，正常卖出
                    sell_price = price_dict[symbol]
                    sell_value = shares * sell_price
                    total_stock_value += sell_value

                    # 记录卖出交易
                    buy_price = self.position_prices.get(symbol, 0)
                    profit_loss = (sell_price - buy_price) * shares if buy_price > 0 else 0
                    profit_loss_pct = ((sell_price - buy_price) / buy_price * 100) if buy_price > 0 else 0

                    sell_record = {
                        'date': date,
                        'action': '卖出',
                        'symbol': symbol,
                        'shares': shares,
                        'price': sell_price,
                        'value': sell_value,
                        'buy_price': buy_price,
                        'profit_loss': profit_loss,
                        'profit_loss_pct': profit_loss_pct
                    }
                    sell_records.append(sell_record)
                    self.trading_records.append(sell_record)
                else:
                    # 无价格数据，记录但按上次价格估算（停牌等情况）
                    unsold_positions.append(symbol)
                    last_price = self.position_prices.get(symbol, 0)
                    if last_price > 0:
                        estimated_value = shares * last_price
                        total_stock_value += estimated_value
                        print(f"  警告: {symbol} 无当日价格数据，按上次价格 {last_price:.2f} 估算卖出")

        self.cash += total_stock_value

        # 强制清空所有持仓，确保持仓数量正确
        self.positions = {}
        self.position_prices = {}

        if unsold_positions:
            print(f"  注意: {len(unsold_positions)} 只股票因无价格数据被强制清仓: {unsold_positions[:3]}{'...' if len(unsold_positions) > 3 else ''}")

        # 按市值排序，选择最小的股票
        day_data.sort(key=lambda x: x['tot_mv'])
        selected_stocks = day_data[:self.max_positions]

        if len(selected_stocks) == 0:
            print("没有符合条件的股票")
            return

        # 等权重分配资金
        available_cash = self.cash * (1 - self.cash_reserve_ratio)
        cash_per_stock = available_cash / len(selected_stocks)

        successful_purchases = 0
        buy_records = []

        for stock in selected_stocks:
            symbol = stock['symbol']
            price = stock['close_price']
            shares = int(cash_per_stock / price)

            if shares > 0:
                cost = shares * price
                self.positions[symbol] = shares
                self.position_prices[symbol] = price  # 记录买入价格
                self.cash -= cost
                successful_purchases += 1

                # 记录买入交易
                buy_record = {
                    'date': date,
                    'action': '买入',
                    'symbol': symbol,
                    'shares': shares,
                    'price': price,
                    'value': cost,
                    'buy_price': price,
                    'profit_loss': 0,
                    'profit_loss_pct': 0
                }
                buy_records.append(buy_record)
                self.trading_records.append(buy_record)

        print(f"卖出 {len(sell_records)} 只股票，买入 {successful_purchases} 只股票")
        print(f"当前持仓数量: {len(self.positions)} 只股票")
        print(f"剩余现金: {self.cash:,.2f}")

        # 验证持仓数量是否正确
        if len(self.positions) > self.max_positions:
            print(f"  ⚠️  警告: 持仓数量 {len(self.positions)} 超过设定的最大持仓 {self.max_positions}")
        elif len(self.positions) < self.max_positions and len(selected_stocks) >= self.max_positions:
            print(f"  ⚠️  警告: 持仓数量 {len(self.positions)} 少于设定的最大持仓 {self.max_positions}")

        # 打印调仓详情
        if sell_records:
            print("卖出详情:")
            for record in sell_records[:5]:  # 只显示前5只
                print(f"  {record['symbol']}: {record['shares']}股 @ {record['price']:.2f} "
                      f"盈亏: {record['profit_loss']:.2f} ({record['profit_loss_pct']:.2f}%)")
            if len(sell_records) > 5:
                print(f"  ... 还有{len(sell_records)-5}只股票")

        if buy_records:
            print("买入详情:")
            for record in buy_records[:5]:  # 只显示前5只
                print(f"  {record['symbol']}: {record['shares']}股 @ {record['price']:.2f}")
            if len(buy_records) > 5:
                print(f"  ... 还有{len(buy_records)-5}只股票")
    
    def calculate_portfolio_value(self, day_data):
        """计算投资组合价值"""
        total_value = self.cash
        
        # 创建价格字典便于查找
        price_dict = {stock['symbol']: stock['close_price'] for stock in day_data}
        
        for symbol, shares in self.positions.items():
            if shares > 0 and symbol in price_dict:
                total_value += shares * price_dict[symbol]
        
        return total_value

def calculate_performance_metrics(portfolio_values):
    """计算回测表现指标"""
    if not portfolio_values:
        return None
    
    # 计算日收益率
    daily_returns = []
    for i in range(1, len(portfolio_values)):
        prev_value = portfolio_values[i-1]['portfolio_value']
        curr_value = portfolio_values[i]['portfolio_value']
        daily_return = (curr_value / prev_value - 1) if prev_value > 0 else 0
        daily_returns.append(daily_return)
    
    # 基本指标
    start_value = portfolio_values[0]['portfolio_value']
    end_value = portfolio_values[-1]['portfolio_value']
    total_return = (end_value / start_value - 1) * 100
    
    # 年化收益率
    days = len(portfolio_values)
    annual_return = ((end_value / start_value) ** (252 / days) - 1) * 100
    
    # 最大回撤
    max_value = start_value
    max_drawdown = 0
    for pv in portfolio_values:
        current_value = pv['portfolio_value']
        if current_value > max_value:
            max_value = current_value
        drawdown = (current_value / max_value - 1) * 100
        if drawdown < max_drawdown:
            max_drawdown = drawdown
    
    # 波动率
    if len(daily_returns) > 1:
        mean_return = sum(daily_returns) / len(daily_returns)
        variance = sum((r - mean_return) ** 2 for r in daily_returns) / (len(daily_returns) - 1)
        volatility = math.sqrt(variance * 252) * 100
    else:
        volatility = 0
    
    # 夏普比率（假设无风险利率3%）
    risk_free_rate = 0.03
    excess_return = annual_return / 100 - risk_free_rate
    sharpe_ratio = excess_return / (volatility / 100) if volatility > 0 else 0
    
    # 胜率
    win_count = sum(1 for r in daily_returns if r > 0)
    win_rate = (win_count / len(daily_returns) * 100) if daily_returns else 0
    
    return {
        'start_value': start_value,
        'end_value': end_value,
        'total_return': total_return,
        'annual_return': annual_return,
        'max_drawdown': max_drawdown,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate,
        'trading_days': days
    }

def save_results_to_csv(portfolio_values, filename='backtest_results.csv'):
    """保存组合价值结果到CSV文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write('date,portfolio_value,cash,stock_value,positions_count\n')

            # 写入数据
            for pv in portfolio_values:
                f.write(f"{pv['date']},{pv['portfolio_value']:.2f},{pv['cash']:.2f},"
                       f"{pv['stock_value']:.2f},{pv['positions_count']}\n")

        print(f"组合价值结果已保存到 {filename}")
    except Exception as e:
        print(f"保存组合价值文件失败: {e}")

def save_trading_records_to_csv(trading_records, filename='trading_records.csv'):
    """保存交易记录到CSV文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write('date,action,symbol,shares,price,value,buy_price,profit_loss,profit_loss_pct\n')

            # 写入数据
            for record in trading_records:
                f.write(f"{record['date']},{record['action']},{record['symbol']},"
                       f"{record['shares']},{record['price']:.4f},{record['value']:.2f},"
                       f"{record['buy_price']:.4f},{record['profit_loss']:.2f},"
                       f"{record['profit_loss_pct']:.2f}\n")

        print(f"交易记录已保存到 {filename}")
    except Exception as e:
        print(f"保存交易记录文件失败: {e}")

def save_positions_summary_to_csv(trading_records, filename='positions_summary.csv'):
    """保存持仓汇总到CSV文件"""
    try:
        # 按调仓日期分组
        rebalance_dates = {}
        for record in trading_records:
            date = record['date']
            if date not in rebalance_dates:
                rebalance_dates[date] = {'买入': [], '卖出': []}
            rebalance_dates[date][record['action']].append(record)

        with open(filename, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write('rebalance_date,action,symbol,shares,price,value,buy_price,profit_loss,profit_loss_pct\n')

            # 按日期排序写入数据
            for date in sorted(rebalance_dates.keys()):
                # 先写卖出记录
                for record in rebalance_dates[date]['卖出']:
                    f.write(f"{date},{record['action']},{record['symbol']},"
                           f"{record['shares']},{record['price']:.4f},{record['value']:.2f},"
                           f"{record['buy_price']:.4f},{record['profit_loss']:.2f},"
                           f"{record['profit_loss_pct']:.2f}\n")

                # 再写买入记录
                for record in rebalance_dates[date]['买入']:
                    f.write(f"{date},{record['action']},{record['symbol']},"
                           f"{record['shares']},{record['price']:.4f},{record['value']:.2f},"
                           f"{record['buy_price']:.4f},{record['profit_loss']:.2f},"
                           f"{record['profit_loss_pct']:.2f}\n")

        print(f"持仓汇总已保存到 {filename}")
    except Exception as e:
        print(f"保存持仓汇总文件失败: {e}")

def show_trading_statistics(trading_records):
    """显示交易统计信息"""
    if not trading_records:
        print("没有交易记录")
        return

    print("\n" + "=" * 60)
    print("交易统计:")
    print("=" * 60)

    # 分离买入和卖出记录
    buy_records = [r for r in trading_records if r['action'] == '买入']
    sell_records = [r for r in trading_records if r['action'] == '卖出']

    print(f"总交易次数: {len(trading_records)}")
    print(f"买入次数: {len(buy_records)}")
    print(f"卖出次数: {len(sell_records)}")

    if sell_records:
        # 计算盈亏统计
        total_profit_loss = sum(r['profit_loss'] for r in sell_records)
        profitable_trades = [r for r in sell_records if r['profit_loss'] > 0]
        losing_trades = [r for r in sell_records if r['profit_loss'] < 0]

        print(f"总盈亏: ¥{total_profit_loss:,.2f}")
        print(f"盈利交易: {len(profitable_trades)} 次")
        print(f"亏损交易: {len(losing_trades)} 次")

        if len(sell_records) > 0:
            win_rate = len(profitable_trades) / len(sell_records) * 100
            print(f"交易胜率: {win_rate:.2f}%")

        if profitable_trades:
            avg_profit = sum(r['profit_loss'] for r in profitable_trades) / len(profitable_trades)
            max_profit = max(r['profit_loss'] for r in profitable_trades)
            print(f"平均盈利: ¥{avg_profit:,.2f}")
            print(f"最大盈利: ¥{max_profit:,.2f}")

        if losing_trades:
            avg_loss = sum(r['profit_loss'] for r in losing_trades) / len(losing_trades)
            max_loss = min(r['profit_loss'] for r in losing_trades)
            print(f"平均亏损: ¥{avg_loss:,.2f}")
            print(f"最大亏损: ¥{max_loss:,.2f}")

    # 显示调仓次数
    rebalance_dates = set(r['date'] for r in trading_records)
    print(f"调仓次数: {len(rebalance_dates)}")

    print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("小市值低市盈率策略回测 - 基于MySQL数据库")
    print("=" * 60)

    # 创建数据库连接
    db_connector = DatabaseConnector(DATABASE_CONFIG)

    try:
        # 从数据库加载数据
        start_date = TIME_CONFIG['start_date']
        end_date = TIME_CONFIG['end_date'] or datetime.now().strftime('%Y-%m-%d')

        print(f"回测期间: {start_date} 到 {end_date}")
        print(f"筛选条件: PE {STRATEGY_CONFIG['pe_min']}-{STRATEGY_CONFIG['pe_max']}")
        print(f"策略参数: 调仓频率{STRATEGY_CONFIG['rebalance_freq']}天, 持仓{STRATEGY_CONFIG['max_positions']}只")

        data = load_stock_data(db_connector, start_date, end_date)

        if not data:
            print("没有数据，退出程序")
            print("请检查:")
            print("1. 数据库连接配置是否正确")
            print("2. 数据库中是否有指定时间段的数据")
            print("3. 数据是否满足筛选条件(PE 0-40)")
            return

        # 运行回测
        strategy = SimpleBacktest(STRATEGY_CONFIG)
        portfolio_values = strategy.run_backtest(data)

        # 计算表现指标
        metrics = calculate_performance_metrics(portfolio_values)

        if metrics:
            print("\n" + "=" * 60)
            print("回测结果:")
            print("=" * 60)
            print(f"回测期间: {start_date} 到 {end_date}")
            print(f"初始资金: ¥{metrics['start_value']:,.2f}")
            print(f"最终资金: ¥{metrics['end_value']:,.2f}")
            print(f"总收益率: {metrics['total_return']:.2f}%")
            print(f"年化收益率: {metrics['annual_return']:.2f}%")
            print(f"最大回撤: {metrics['max_drawdown']:.2f}%")
            print(f"年化波动率: {metrics['volatility']:.2f}%")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"胜率: {metrics['win_rate']:.2f}%")
            print(f"交易天数: {metrics['trading_days']}")
            print("=" * 60)

            # 显示交易统计
            show_trading_statistics(strategy.trading_records)

            # 保存结果
            if OUTPUT_CONFIG['save_csv']:
                save_results_to_csv(portfolio_values, OUTPUT_CONFIG['csv_filename'])
                save_trading_records_to_csv(strategy.trading_records, OUTPUT_CONFIG['trading_records_filename'])
                save_positions_summary_to_csv(strategy.trading_records, OUTPUT_CONFIG['positions_summary_filename'])

            # 显示最后几天的组合状态
            print("\n最后5天的组合状态:")
            print("日期\t\t组合价值\t现金\t\t股票价值\t持仓数")
            print("-" * 60)
            for pv in portfolio_values[-5:]:
                print(f"{pv['date']}\t{pv['portfolio_value']:,.0f}\t\t{pv['cash']:,.0f}\t\t"
                      f"{pv['stock_value']:,.0f}\t\t{pv['positions_count']}")

        else:
            print("计算表现指标失败")

    finally:
        # 关闭数据库连接
        db_connector.close()

if __name__ == "__main__":
    main()
