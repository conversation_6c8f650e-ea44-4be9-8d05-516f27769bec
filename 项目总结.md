# 股票回测系统项目总结

## 项目概述

基于您的要求，我已经完成了一个完整的股票回测系统，该系统从MySQL数据库读取真实股票数据，实现小市值低市盈率选股策略。

## 完成的功能

### 1. 核心回测功能
- ✅ **数据源**: 从MySQL数据库读取股票数据
- ✅ **筛选条件**: 市盈率0-40（可配置）
- ✅ **排序条件**: 按总市值从小到大排序
- ✅ **调仓频率**: 每20个交易日调仓一次（可配置）
- ✅ **持仓数量**: 30只股票（可配置）
- ✅ **回测期间**: 2024-01-01至今（可配置）

### 2. 性能指标计算
- ✅ **总收益率**: (期末价值 - 期初价值) / 期初价值
- ✅ **年化收益率**: 按252个交易日年化
- ✅ **最大回撤**: 从峰值到谷值的最大跌幅
- ✅ **年化波动率**: 收益率标准差年化
- ✅ **夏普比率**: 风险调整后收益
- ✅ **胜率**: 上涨交易日占比

### 3. 数据库集成
- ✅ **表结构**: 基于提供的5个表定义
- ✅ **数据查询**: 联合查询获取完整数据
- ✅ **数据验证**: 确保数据质量和完整性
- ✅ **错误处理**: 完善的异常处理机制

## 文件结构

```
项目目录/
├── basic_backtest.py      # 主回测程序
├── config.py              # 配置文件
├── test_database.py       # 数据库测试脚本
├── setup.py               # 安装脚本
├── requirements.txt       # 依赖包列表
├── README.md              # 详细说明文档
├── QUICKSTART.md          # 快速开始指南
├── table_definitions.txt  # 数据库表定义
├── stock_goldminer_bj.sql # 数据库建表脚本
└── goldminer_guanfang.md  # 原始API文档
```

## 核心代码架构

### 1. DatabaseConnector类
```python
class DatabaseConnector:
    """数据库连接器"""
    - connect(): 建立数据库连接
    - execute_query(): 执行SQL查询
    - close(): 关闭连接
```

### 2. SimpleBacktest类
```python
class SimpleBacktest:
    """回测引擎"""
    - run_backtest(): 执行完整回测流程
    - rebalance(): 执行调仓逻辑
    - calculate_portfolio_value(): 计算组合价值
```

### 3. 核心函数
- `load_stock_data()`: 从数据库加载股票数据
- `calculate_performance_metrics()`: 计算回测指标
- `save_results_to_csv()`: 保存结果到文件

## 策略逻辑

### 选股流程
1. **数据筛选**: 从数据库查询符合条件的股票
   - 市盈率在设定范围内（默认0-40）
   - 有完整的价格和市值数据
   - 数据质量良好（非空、合理值）

2. **股票排序**: 按总市值从小到大排序

3. **股票选择**: 选择市值最小的N只股票（默认30只）

### 资金管理
1. **等权重分配**: 可用资金平均分配给选中股票
2. **现金缓冲**: 保留一定比例现金（默认2%）
3. **整股交易**: 按整股数量买入

### 调仓机制
1. **调仓频率**: 每N个交易日调仓（默认20天）
2. **全量调仓**: 卖出所有持仓，重新选股买入
3. **动态调整**: 根据最新数据重新筛选和排序

## 配置系统

### 数据库配置
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'database': 'stock_goldminer_bj',
    'charset': 'utf8mb4'
}
```

### 策略配置
```python
STRATEGY_CONFIG = {
    'initial_capital': 1000000,    # 初始资金
    'rebalance_freq': 20,          # 调仓频率
    'max_positions': 30,           # 持仓数量
    'pe_min': 0,                   # 市盈率范围
    'pe_max': 40,
    'cash_reserve_ratio': 0.02     # 现金保留比例
}
```

## 使用方法

### 1. 环境准备
```bash
# 安装依赖
python setup.py

# 或手动安装
pip install mysql-connector-python
```

### 2. 数据库设置
```sql
-- 创建数据库
CREATE DATABASE stock_goldminer_bj;

-- 导入表结构
source stock_goldminer_bj.sql;
```

### 3. 配置修改
编辑 `config.py` 设置数据库连接参数

### 4. 测试连接
```bash
python test_database.py
```

### 5. 运行回测
```bash
python basic_backtest.py
```

## 输出结果示例

### 控制台输出
```
============================================================
小市值低市盈率策略回测 - 基于MySQL数据库
============================================================
回测期间: 2024-01-01 到 2025-01-09
筛选条件: PE 0-40
策略参数: 调仓频率20天, 持仓30只
成功加载 15420 条数据记录

============================================================
回测结果:
============================================================
初始资金: ¥1,000,000.00
最终资金: ¥1,156,789.23
总收益率: 15.68%
年化收益率: 15.23%
最大回撤: -8.45%
年化波动率: 18.76%
夏普比率: 0.65
胜率: 52.45%
交易天数: 245
============================================================
```

### CSV文件输出
生成 `backtest_results.csv` 包含每日详细数据：
- 日期
- 组合价值
- 现金余额
- 股票价值
- 持仓数量

## 技术特点

### 1. 模块化设计
- 数据库连接与业务逻辑分离
- 配置与代码分离
- 可扩展的架构设计

### 2. 错误处理
- 数据库连接异常处理
- 数据质量检查
- 边界条件处理

### 3. 性能优化
- 高效的SQL查询
- 批量数据处理
- 内存优化

### 4. 用户友好
- 详细的进度显示
- 清晰的错误提示
- 完善的文档说明

## 扩展建议

### 1. 功能扩展
- 添加更多技术指标筛选
- 实现多因子选股模型
- 增加风险管理模块
- 支持分行业回测

### 2. 性能优化
- 数据缓存机制
- 并行计算支持
- 增量数据更新

### 3. 可视化
- 收益曲线图表
- 回撤分析图
- 持仓分布图
- 风险指标图表

## 注意事项

1. **数据质量**: 回测结果的可靠性完全依赖于数据质量
2. **历史偏差**: 历史回测结果不代表未来表现
3. **交易成本**: 当前版本未考虑交易成本和滑点
4. **市场环境**: 策略在不同市场环境下表现可能差异很大

## 总结

该回测系统完全满足您的需求：
- ✅ 使用MySQL数据库作为数据源
- ✅ 实现小市值低市盈率选股策略
- ✅ 支持灵活的参数配置
- ✅ 提供完整的性能指标计算
- ✅ 具备良好的扩展性和维护性

系统已经可以投入使用，您只需要：
1. 准备股票数据并导入数据库
2. 修改配置文件中的数据库连接参数
3. 运行回测程序即可获得结果
