"""
股票回测策略：基于市盈率和市值的选股策略
筛选条件：市盈率0-40
排序条件：市值最小
调仓频率：每20个交易日
持仓数量：30只股票
回测期间：2025-01-01至今
"""

import pandas as pd
import numpy as np
import sqlite3
import mysql.connector
from datetime import datetime, timedelta
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
import warnings
warnings.filterwarnings('ignore')

class DataLoader:
    """数据加载器"""
    
    def __init__(self, db_config=None):
        """
        初始化数据库连接
        db_config: 数据库配置字典，如果为None则使用SQLite
        """
        self.db_config = db_config
        
    def get_connection(self):
        """获取数据库连接"""
        if self.db_config:
            # MySQL连接
            return mysql.connector.connect(**self.db_config)
        else:
            # 如果没有MySQL配置，创建示例SQLite数据库
            return sqlite3.connect('stock_data.db')
    
    def load_stock_data(self, start_date='2025-01-01', end_date=None):
        """
        加载股票数据
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
            
        conn = self.get_connection()
        
        # 合并查询所有需要的数据
        query = """
        SELECT 
            dv.symbol,
            dv.trade_date,
            dv.pe_ttm,
            dm.tot_mv,
            db.tclose as close_price,
            db.turnrate
        FROM daily_valuation dv
        LEFT JOIN daily_mktvalue dm ON dv.symbol = dm.symbol AND dv.trade_date = dm.trade_date
        LEFT JOIN daily_basic db ON dv.symbol = db.symbol AND dv.trade_date = db.trade_date
        WHERE dv.trade_date >= %s AND dv.trade_date <= %s
        AND dv.pe_ttm > 0 AND dv.pe_ttm <= 40
        AND dm.tot_mv IS NOT NULL
        AND db.tclose IS NOT NULL
        ORDER BY dv.trade_date, dm.tot_mv
        """
        
        try:
            df = pd.read_sql(query, conn, params=[start_date, end_date])
            conn.close()
            return df
        except Exception as e:
            print(f"数据加载错误: {e}")
            # 如果数据库查询失败，生成模拟数据用于测试
            return self.generate_sample_data(start_date, end_date)
    
    def generate_sample_data(self, start_date, end_date):
        """生成示例数据用于测试"""
        print("正在生成示例数据...")
        
        # 生成日期范围
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        # 只保留工作日
        date_range = date_range[date_range.weekday < 5]
        
        # 生成50只股票的示例数据
        symbols = [f"00000{i:02d}" for i in range(1, 51)]
        
        data = []
        np.random.seed(42)  # 固定随机种子
        
        for date in date_range:
            for symbol in symbols:
                # 生成随机但合理的数据
                pe_ttm = np.random.uniform(5, 35)  # 市盈率5-35
                tot_mv = np.random.uniform(1e9, 1e12)  # 市值1亿-1万亿
                close_price = np.random.uniform(5, 100)  # 股价5-100
                turnrate = np.random.uniform(0.1, 5.0)  # 换手率
                
                data.append({
                    'symbol': symbol,
                    'trade_date': date.strftime('%Y-%m-%d'),
                    'pe_ttm': pe_ttm,
                    'tot_mv': tot_mv,
                    'close_price': close_price,
                    'turnrate': turnrate
                })
        
        return pd.DataFrame(data)

class SmallCapLowPEStrategy(Strategy):
    """小市值低市盈率策略"""
    
    # 策略参数
    rebalance_freq = 20  # 调仓频率（交易日）
    max_positions = 30   # 最大持仓数量
    
    def init(self):
        """初始化策略"""
        self.rebalance_counter = 0
        self.current_positions = []
        self.last_rebalance_date = None
        
    def next(self):
        """策略主逻辑"""
        current_date = self.data.index[-1]
        
        # 检查是否需要调仓
        if self.should_rebalance(current_date):
            self.rebalance(current_date)
            self.last_rebalance_date = current_date
            
    def should_rebalance(self, current_date):
        """判断是否需要调仓"""
        if self.last_rebalance_date is None:
            return True
            
        # 计算距离上次调仓的交易日数
        days_since_rebalance = len(self.data.index[
            self.data.index > self.last_rebalance_date
        ])
        
        return days_since_rebalance >= self.rebalance_freq
    
    def rebalance(self, current_date):
        """执行调仓"""
        # 这里需要根据实际的backtesting.py框架API来实现
        # 由于backtesting.py主要用于单一资产回测，多资产组合需要自定义实现
        pass

class PortfolioBacktest:
    """投资组合回测类"""
    
    def __init__(self, data_loader):
        self.data_loader = data_loader
        self.portfolio_value = []
        self.positions = {}
        self.cash = 1000000  # 初始资金100万
        self.initial_cash = self.cash
        
    def run_backtest(self, start_date='2025-01-01', end_date=None):
        """运行回测"""
        print("开始加载数据...")
        df = self.data_loader.load_stock_data(start_date, end_date)
        
        if df.empty:
            print("没有找到符合条件的数据")
            return None
            
        print(f"加载了 {len(df)} 条数据记录")
        
        # 按日期分组
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        daily_data = df.groupby('trade_date')
        
        dates = sorted(daily_data.groups.keys())
        rebalance_dates = dates[::20]  # 每20个交易日调仓
        
        print(f"回测期间: {dates[0]} 到 {dates[-1]}")
        print(f"调仓日期数量: {len(rebalance_dates)}")
        
        portfolio_values = []
        
        for i, date in enumerate(dates):
            day_data = daily_data.get_group(date)
            
            # 检查是否为调仓日
            if date in rebalance_dates:
                self.rebalance(day_data)
                
            # 计算当日组合价值
            portfolio_value = self.calculate_portfolio_value(day_data)
            portfolio_values.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'cash': self.cash
            })
            
        self.portfolio_value = pd.DataFrame(portfolio_values)
        return self.calculate_performance()
    
    def rebalance(self, day_data):
        """执行调仓"""
        # 筛选条件：市盈率0-40（已在数据加载时筛选）
        # 排序条件：市值最小
        selected_stocks = day_data.nsmallest(30, 'tot_mv')
        
        # 清空现有持仓
        self.cash += sum(self.positions.values())
        self.positions = {}
        
        # 等权重分配资金
        if len(selected_stocks) > 0:
            weight_per_stock = self.cash / len(selected_stocks)
            
            for _, stock in selected_stocks.iterrows():
                symbol = stock['symbol']
                price = stock['close_price']
                shares = int(weight_per_stock / price)
                
                if shares > 0:
                    position_value = shares * price
                    self.positions[symbol] = position_value
                    self.cash -= position_value
    
    def calculate_portfolio_value(self, day_data):
        """计算投资组合价值"""
        total_value = self.cash
        
        # 更新持仓价值
        for symbol in list(self.positions.keys()):
            stock_data = day_data[day_data['symbol'] == symbol]
            if not stock_data.empty:
                current_price = stock_data.iloc[0]['close_price']
                # 假设按股数持有，这里简化处理
                shares = self.positions[symbol] / current_price
                self.positions[symbol] = shares * current_price
                total_value += self.positions[symbol]
            else:
                # 如果股票停牌或退市，保持原价值
                total_value += self.positions[symbol]
                
        return total_value
    
    def calculate_performance(self):
        """计算回测表现"""
        if self.portfolio_value.empty:
            return None
            
        df = self.portfolio_value.copy()
        df['returns'] = df['portfolio_value'].pct_change()
        
        # 计算总收益率
        total_return = (df['portfolio_value'].iloc[-1] / df['portfolio_value'].iloc[0] - 1) * 100
        
        # 计算年化收益率
        days = (df['date'].iloc[-1] - df['date'].iloc[0]).days
        annual_return = ((df['portfolio_value'].iloc[-1] / df['portfolio_value'].iloc[0]) ** (365/days) - 1) * 100
        
        # 计算最大回撤
        df['cummax'] = df['portfolio_value'].cummax()
        df['drawdown'] = (df['portfolio_value'] / df['cummax'] - 1) * 100
        max_drawdown = df['drawdown'].min()
        
        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        excess_returns = df['returns'].dropna() - risk_free_rate/252
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() != 0 else 0
        
        results = {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'start_value': df['portfolio_value'].iloc[0],
            'end_value': df['portfolio_value'].iloc[-1],
            'trading_days': len(df),
            'portfolio_data': df
        }
        
        return results

def main():
    """主函数"""
    # 数据库配置（请根据实际情况修改）
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'password',
        'database': 'stock_goldminer_bj',
        'charset': 'utf8mb4'
    }
    
    # 如果没有MySQL数据库，设置为None使用示例数据
    data_loader = DataLoader(db_config=None)  # 使用示例数据
    
    # 创建回测实例
    backtest = PortfolioBacktest(data_loader)
    
    # 运行回测
    print("=" * 50)
    print("开始运行回测...")
    results = backtest.run_backtest(start_date='2025-01-01')
    
    if results:
        print("=" * 50)
        print("回测结果:")
        print(f"总收益率: {results['total_return']:.2f}%")
        print(f"年化收益率: {results['annual_return']:.2f}%")
        print(f"最大回撤: {results['max_drawdown']:.2f}%")
        print(f"夏普比率: {results['sharpe_ratio']:.2f}")
        print(f"初始资金: {results['start_value']:,.2f}")
        print(f"最终资金: {results['end_value']:,.2f}")
        print(f"交易天数: {results['trading_days']}")
        print("=" * 50)
        
        # 保存结果到文件
        results['portfolio_data'].to_csv('backtest_results.csv', index=False)
        print("回测结果已保存到 backtest_results.csv")
    else:
        print("回测失败，请检查数据")

if __name__ == "__main__":
    main()
