"""
数据库连接测试脚本
用于验证数据库连接和数据完整性
"""

from datetime import datetime
try:
    from config import DATABASE_CONFIG, STRATEGY_CONFIG
except ImportError:
    print("未找到config.py，使用默认配置")
    DATABASE_CONFIG = {
        'host': 'localhost',
        'user': 'root',
        'password': 'your_password',
        'database': 'stock_goldminer_bj',
        'charset': 'utf8mb4'
    }
    STRATEGY_CONFIG = {
        'pe_min': 0,
        'pe_max': 40
    }

def test_database_connection():
    """测试数据库连接"""
    try:
        import mysql.connector
        print("正在测试数据库连接...")
        
        conn = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = conn.cursor()
        
        # 测试连接
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✓ 数据库连接成功，MySQL版本: {version[0]}")
        
        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE %s", (DATABASE_CONFIG['database'],))
        if cursor.fetchone():
            print(f"✓ 数据库 '{DATABASE_CONFIG['database']}' 存在")
        else:
            print(f"✗ 数据库 '{DATABASE_CONFIG['database']}' 不存在")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("✗ 请安装mysql-connector-python: pip install mysql-connector-python")
        return False
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_tables():
    """测试数据表是否存在"""
    try:
        import mysql.connector
        conn = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = conn.cursor()
        
        required_tables = ['daily_basic', 'daily_mktvalue', 'daily_valuation']
        
        print("\n检查数据表...")
        for table in required_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                print(f"✓ 表 '{table}' 存在")
            else:
                print(f"✗ 表 '{table}' 不存在")
                return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 检查数据表失败: {e}")
        return False

def test_data_availability():
    """测试数据可用性"""
    try:
        import mysql.connector
        conn = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        print("\n检查数据可用性...")
        
        # 检查数据日期范围
        cursor.execute("""
            SELECT 
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date,
                COUNT(DISTINCT trade_date) as trading_days,
                COUNT(DISTINCT symbol) as stock_count
            FROM daily_valuation
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"✓ 数据日期范围: {result['min_date']} 到 {result['max_date']}")
            print(f"✓ 交易日数量: {result['trading_days']}")
            print(f"✓ 股票数量: {result['stock_count']}")
        else:
            print("✗ 没有找到数据")
            return False
        
        # 检查符合条件的数据
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM daily_valuation dv
            INNER JOIN daily_mktvalue dm ON dv.symbol = dm.symbol AND dv.trade_date = dm.trade_date
            INNER JOIN daily_basic db ON dv.symbol = db.symbol AND dv.trade_date = db.trade_date
            WHERE dv.pe_ttm > %s AND dv.pe_ttm <= %s
                AND dm.tot_mv IS NOT NULL
                AND db.tclose IS NOT NULL
                AND db.tclose > 0
        """, (STRATEGY_CONFIG['pe_min'], STRATEGY_CONFIG['pe_max']))
        
        result = cursor.fetchone()
        if result and result['count'] > 0:
            print(f"✓ 符合筛选条件的数据记录: {result['count']}")
        else:
            print("✗ 没有符合筛选条件的数据")
            return False
        
        # 检查最近的数据
        cursor.execute("""
            SELECT trade_date, COUNT(*) as stock_count
            FROM daily_valuation
            WHERE pe_ttm > %s AND pe_ttm <= %s
            ORDER BY trade_date DESC
            LIMIT 5
        """, (STRATEGY_CONFIG['pe_min'], STRATEGY_CONFIG['pe_max']))
        
        results = cursor.fetchall()
        if results:
            print("\n最近5个交易日的数据:")
            for row in results:
                print(f"  {row['trade_date']}: {row['stock_count']} 只股票")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 检查数据可用性失败: {e}")
        return False

def test_sample_query():
    """测试示例查询"""
    try:
        import mysql.connector
        conn = mysql.connector.connect(**DATABASE_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        print("\n执行示例查询...")
        
        # 获取最新交易日的小市值股票
        cursor.execute("""
            SELECT 
                dv.symbol,
                dv.trade_date,
                dv.pe_ttm,
                dm.tot_mv,
                db.tclose as close_price
            FROM daily_valuation dv
            INNER JOIN daily_mktvalue dm ON dv.symbol = dm.symbol AND dv.trade_date = dm.trade_date
            INNER JOIN daily_basic db ON dv.symbol = db.symbol AND dv.trade_date = db.trade_date
            WHERE dv.pe_ttm > %s AND dv.pe_ttm <= %s
                AND dm.tot_mv IS NOT NULL
                AND db.tclose IS NOT NULL
                AND db.tclose > 0
            ORDER BY dv.trade_date DESC, dm.tot_mv ASC
            LIMIT 10
        """, (STRATEGY_CONFIG['pe_min'], STRATEGY_CONFIG['pe_max']))
        
        results = cursor.fetchall()
        if results:
            print("✓ 示例查询成功，最新小市值股票前10只:")
            print("股票代码\t交易日期\t市盈率\t总市值(亿)\t收盘价")
            print("-" * 60)
            for row in results:
                mv_yi = row['tot_mv'] / 1e8  # 转换为亿元
                print(f"{row['symbol']}\t{row['trade_date']}\t{row['pe_ttm']:.2f}\t{mv_yi:.2f}\t\t{row['close_price']:.2f}")
        else:
            print("✗ 示例查询无结果")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 示例查询失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("数据库连接和数据完整性测试")
    print("=" * 60)
    
    # 测试步骤
    tests = [
        ("数据库连接", test_database_connection),
        ("数据表检查", test_tables),
        ("数据可用性", test_data_availability),
        ("示例查询", test_sample_query)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if not test_func():
            all_passed = False
            print(f"✗ {test_name} 失败")
        else:
            print(f"✓ {test_name} 通过")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ 所有测试通过，可以运行回测程序")
    else:
        print("✗ 部分测试失败，请检查配置和数据")
        print("\n解决建议:")
        print("1. 检查config.py中的数据库连接配置")
        print("2. 确保数据库和表已正确创建")
        print("3. 确保数据库中有足够的历史数据")
        print("4. 检查数据质量和完整性")
    print("=" * 60)

if __name__ == "__main__":
    main()
