# 快速开始指南

## 1. 环境准备

### 系统要求
- Python 3.6+
- MySQL 5.7+ 或 8.0+

### 安装依赖
```bash
# 方法1: 使用安装脚本（推荐）
python setup.py

# 方法2: 手动安装
pip install mysql-connector-python
```

## 2. 数据库设置

### 创建数据库
```sql
CREATE DATABASE stock_goldminer_bj CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
```

### 导入表结构
```bash
mysql -u root -p stock_goldminer_bj < stock_goldminer_bj.sql
```

### 导入数据
您需要准备以下表的数据：
- `daily_basic` - 股票基础数据（收盘价、换手率等）
- `daily_mktvalue` - 市值数据
- `daily_valuation` - 估值数据（市盈率等）

数据格式请参考 `table_definitions.txt`

## 3. 配置系统

### 修改数据库配置
编辑 `config.py` 文件：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '你的密码',  # 修改这里
    'database': 'stock_goldminer_bj',
    'charset': 'utf8mb4'
}
```

### 调整策略参数（可选）
```python
STRATEGY_CONFIG = {
    'initial_capital': 1000000,    # 初始资金
    'rebalance_freq': 20,          # 调仓频率
    'max_positions': 30,           # 持仓数量
    'pe_min': 0,                   # 市盈率范围
    'pe_max': 40,
}
```

## 4. 测试连接

```bash
python test_database.py
```

期望输出：
```
============================================================
数据库连接和数据完整性测试
============================================================

数据库连接:
✓ 数据库连接成功，MySQL版本: 8.0.33
✓ 数据库 'stock_goldminer_bj' 存在
✓ 数据库连接 通过

数据表检查:
✓ 表 'daily_basic' 存在
✓ 表 'daily_mktvalue' 存在
✓ 表 'daily_valuation' 存在
✓ 数据表检查 通过

数据可用性:
✓ 数据日期范围: 2024-01-02 到 2025-01-08
✓ 交易日数量: 245
✓ 股票数量: 4500
✓ 符合筛选条件的数据记录: 15420
✓ 数据可用性 通过

✓ 所有测试通过，可以运行回测程序
```

## 5. 运行回测

```bash
python basic_backtest.py
```

## 6. 查看结果

### 控制台输出
```
============================================================
小市值低市盈率策略回测 - 基于MySQL数据库
============================================================
回测期间: 2024-01-01 到 2025-01-09
筛选条件: PE 0-40
策略参数: 调仓频率20天, 持仓30只
成功加载 15420 条数据记录

============================================================
回测结果:
============================================================
初始资金: ¥1,000,000.00
最终资金: ¥1,156,789.23
总收益率: 15.68%
年化收益率: 15.23%
最大回撤: -8.45%
年化波动率: 18.76%
夏普比率: 0.65
胜率: 52.45%
交易天数: 245
============================================================
```

### CSV文件
系统会生成 `backtest_results.csv` 文件，包含每日详细数据。

## 常见问题

### Q1: 数据库连接失败
**A:** 检查以下项目：
- MySQL服务是否启动
- 用户名密码是否正确
- 数据库是否存在
- 网络连接是否正常

### Q2: 没有符合条件的数据
**A:** 可能原因：
- 数据库中没有数据
- 市盈率筛选条件过严
- 数据质量问题（NULL值过多）

### Q3: 回测结果异常
**A:** 检查：
- 数据完整性
- 价格数据是否合理
- 调仓频率设置
- 持仓数量设置

### Q4: 性能问题
**A:** 优化建议：
- 为数据库表添加索引
- 减少查询的日期范围
- 优化SQL查询语句

## 进阶使用

### 自定义策略参数
修改 `config.py` 中的参数来测试不同策略：

```python
# 测试不同的市盈率范围
STRATEGY_CONFIG = {
    'pe_min': 5,
    'pe_max': 20,  # 更严格的PE筛选
    'max_positions': 20,  # 更集中的持仓
}

# 测试不同的调仓频率
STRATEGY_CONFIG = {
    'rebalance_freq': 10,  # 更频繁的调仓
}
```

### 批量测试
可以编写脚本来测试不同参数组合的效果。

### 数据分析
使用生成的CSV文件进行更深入的分析：
- 收益分布分析
- 回撤分析
- 行业分布分析
- 风险归因分析

## 技术支持

如遇到问题，请检查：
1. 系统日志和错误信息
2. 数据库连接和数据质量
3. 配置文件设置
4. Python环境和依赖包版本
