"""
调试持仓数量问题的脚本
分析trading_records.csv文件，验证持仓逻辑是否正确
"""

import csv
from collections import defaultdict
from datetime import datetime

def analyze_trading_records(filename='trading_records.csv'):
    """分析交易记录，检查持仓数量逻辑"""
    
    if not filename:
        print("请先运行回测生成trading_records.csv文件")
        return
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            records = list(reader)
    except FileNotFoundError:
        print(f"文件 {filename} 不存在，请先运行回测")
        return
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    if not records:
        print("交易记录为空")
        return
    
    print("=" * 80)
    print("持仓数量分析")
    print("=" * 80)
    
    # 按日期分组
    daily_records = defaultdict(list)
    for record in records:
        daily_records[record['date']].append(record)
    
    # 模拟持仓变化
    positions = {}  # {symbol: shares}
    max_positions_setting = 10  # 从配置中获取
    
    print(f"设定的最大持仓数量: {max_positions_setting}")
    print("\n日期\t\t卖出数\t买入数\t持仓数\t状态")
    print("-" * 80)
    
    for date in sorted(daily_records.keys()):
        day_records = daily_records[date]
        
        sell_count = 0
        buy_count = 0
        sell_symbols = set()
        buy_symbols = set()
        
        # 处理当日交易
        for record in day_records:
            symbol = record['symbol']
            shares = int(record['shares'])
            
            if record['action'] == '卖出':
                sell_count += 1
                sell_symbols.add(symbol)
                # 从持仓中移除
                if symbol in positions:
                    del positions[symbol]
                else:
                    print(f"  ⚠️  警告: 尝试卖出不存在的持仓 {symbol}")
            
            elif record['action'] == '买入':
                buy_count += 1
                buy_symbols.add(symbol)
                # 添加到持仓
                if symbol in positions:
                    print(f"  ⚠️  警告: 重复买入已持有的股票 {symbol}")
                positions[symbol] = shares
        
        current_positions = len(positions)
        
        # 判断状态
        status = "正常"
        if current_positions > max_positions_setting:
            status = f"❌ 超出限制({current_positions - max_positions_setting})"
        elif current_positions < max_positions_setting and buy_count > 0:
            status = f"⚠️  持仓不足({max_positions_setting - current_positions})"
        
        print(f"{date}\t{sell_count}\t{buy_count}\t{current_positions}\t{status}")
        
        # 检查是否有重复的股票
        overlap = sell_symbols & buy_symbols
        if overlap:
            print(f"  ⚠️  当日既卖出又买入的股票: {overlap}")
    
    print("\n" + "=" * 80)
    print("最终持仓分析")
    print("=" * 80)
    
    if positions:
        print(f"最终持仓数量: {len(positions)}")
        print("持仓股票:")
        for i, (symbol, shares) in enumerate(sorted(positions.items()), 1):
            print(f"  {i:2d}. {symbol}: {shares} 股")
            if i >= 20:  # 只显示前20只
                print(f"  ... 还有 {len(positions) - 20} 只股票")
                break
    else:
        print("最终无持仓")

def check_rebalance_logic():
    """检查调仓逻辑的理论正确性"""
    print("\n" + "=" * 80)
    print("调仓逻辑检查")
    print("=" * 80)
    
    print("理论上正确的调仓逻辑应该是:")
    print("1. 每次调仓时，先卖出所有现有持仓")
    print("2. 然后买入新选择的股票")
    print("3. 买入股票数量应该等于设定的最大持仓数量")
    print("4. 如果某只股票既在卖出列表又在买入列表，说明逻辑有问题")
    
    print("\n可能的问题原因:")
    print("1. 部分股票停牌或无价格数据，无法卖出")
    print("2. 卖出逻辑不完整，没有强制清空所有持仓")
    print("3. 买入时资金不足，无法买入足够数量的股票")
    print("4. 数据质量问题，某些股票价格为0或异常")

def suggest_fixes():
    """建议修复方案"""
    print("\n" + "=" * 80)
    print("建议的修复方案")
    print("=" * 80)
    
    print("1. 强制清空持仓:")
    print("   - 每次调仓时，无论是否有价格数据，都强制清空positions字典")
    print("   - 对于无价格数据的股票，按上次价格估算价值")
    
    print("\n2. 验证持仓数量:")
    print("   - 调仓后检查持仓数量是否等于预期")
    print("   - 如果不等于，输出警告信息")
    
    print("\n3. 改进日志输出:")
    print("   - 显示调仓前后的持仓数量")
    print("   - 显示无法卖出的股票列表")
    
    print("\n4. 数据质量检查:")
    print("   - 过滤掉价格为0或异常的股票")
    print("   - 记录数据质量问题")

def main():
    """主函数"""
    print("持仓数量调试工具")
    
    # 分析交易记录
    analyze_trading_records()
    
    # 检查逻辑
    check_rebalance_logic()
    
    # 建议修复
    suggest_fixes()
    
    print("\n" + "=" * 80)
    print("使用建议:")
    print("1. 运行 python basic_backtest.py 生成新的交易记录")
    print("2. 观察控制台输出中的持仓数量信息")
    print("3. 检查是否还有持仓数量异常的情况")
    print("4. 如果问题仍然存在，请检查数据质量")
    print("=" * 80)

if __name__ == "__main__":
    main()
