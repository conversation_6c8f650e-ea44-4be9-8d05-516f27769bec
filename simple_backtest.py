"""
简化版股票回测策略
筛选条件：市盈率0-40
排序条件：市值最小
调仓频率：每20个交易日
持仓数量：30只股票
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def generate_sample_data(start_date='2025-01-01', end_date=None):
    """生成示例股票数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    print("正在生成示例数据...")
    
    # 生成交易日期（排除周末）
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # B表示工作日
    
    # 生成100只股票
    symbols = [f"SH60{i:04d}" for i in range(1, 101)]
    
    data = []
    np.random.seed(42)  # 固定随机种子确保结果可重现
    
    # 为每只股票生成基础属性
    stock_base_data = {}
    for symbol in symbols:
        stock_base_data[symbol] = {
            'base_pe': np.random.uniform(8, 35),
            'base_mv': np.random.uniform(5e8, 5e11),  # 5亿到5000亿
            'base_price': np.random.uniform(10, 100),
            'volatility': np.random.uniform(0.15, 0.4)  # 年化波动率
        }
    
    for i, date in enumerate(date_range):
        for symbol in symbols:
            base_data = stock_base_data[symbol]
            
            # 添加随机波动
            price_change = np.random.normal(0, base_data['volatility'] / np.sqrt(252))
            current_price = base_data['base_price'] * (1 + price_change * (i + 1) * 0.1)
            current_price = max(current_price, 1)  # 价格不能为负
            
            # 市盈率有一定波动
            pe_ttm = base_data['base_pe'] * np.random.uniform(0.8, 1.2)
            pe_ttm = max(pe_ttm, 1)  # 确保PE为正
            
            # 市值随股价变动
            tot_mv = base_data['base_mv'] * (current_price / base_data['base_price'])
            
            # 只保留PE在0-40范围内的股票
            if 0 < pe_ttm <= 40:
                data.append({
                    'symbol': symbol,
                    'trade_date': date,
                    'pe_ttm': pe_ttm,
                    'tot_mv': tot_mv,
                    'close_price': current_price,
                    'turnrate': np.random.uniform(0.5, 8.0)
                })
    
    df = pd.DataFrame(data)
    print(f"生成了 {len(df)} 条数据记录")
    return df

class SmallCapStrategy:
    """小市值策略回测"""
    
    def __init__(self, initial_capital=1000000):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}  # {symbol: shares}
        self.portfolio_values = []
        self.rebalance_freq = 20  # 每20个交易日调仓
        self.max_positions = 30   # 最大持仓30只
        
    def run_backtest(self, data):
        """运行回测"""
        # 按日期分组
        daily_data = data.groupby('trade_date')
        dates = sorted(daily_data.groups.keys())
        
        print(f"回测期间: {dates[0].strftime('%Y-%m-%d')} 到 {dates[-1].strftime('%Y-%m-%d')}")
        print(f"总交易日数: {len(dates)}")
        
        rebalance_dates = dates[::self.rebalance_freq]  # 每20天调仓
        print(f"调仓次数: {len(rebalance_dates)}")
        
        for i, date in enumerate(dates):
            day_data = daily_data.get_group(date)
            
            # 检查是否为调仓日
            if date in rebalance_dates:
                self.rebalance(day_data, date)
            
            # 计算当日组合价值
            portfolio_value = self.calculate_portfolio_value(day_data)
            
            self.portfolio_values.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'cash': self.cash,
                'stock_value': portfolio_value - self.cash,
                'positions_count': len([p for p in self.positions.values() if p > 0])
            })
            
            if i % 50 == 0:  # 每50天打印一次进度
                print(f"进度: {i+1}/{len(dates)}, 组合价值: {portfolio_value:,.2f}")
        
        return pd.DataFrame(self.portfolio_values)
    
    def rebalance(self, day_data, date):
        """执行调仓"""
        print(f"\n调仓日期: {date.strftime('%Y-%m-%d')}")
        
        # 卖出所有持仓
        total_stock_value = 0
        for symbol, shares in self.positions.items():
            if shares > 0:
                stock_price_data = day_data[day_data['symbol'] == symbol]
                if not stock_price_data.empty:
                    price = stock_price_data.iloc[0]['close_price']
                    total_stock_value += shares * price
        
        self.cash += total_stock_value
        self.positions = {}
        
        # 选择市值最小的30只股票
        selected_stocks = day_data.nsmallest(self.max_positions, 'tot_mv')
        
        if len(selected_stocks) == 0:
            print("没有符合条件的股票")
            return
        
        # 等权重分配资金
        cash_per_stock = self.cash * 0.98 / len(selected_stocks)  # 保留2%现金
        
        successful_purchases = 0
        for _, stock in selected_stocks.iterrows():
            symbol = stock['symbol']
            price = stock['close_price']
            shares = int(cash_per_stock / price)
            
            if shares > 0:
                cost = shares * price
                self.positions[symbol] = shares
                self.cash -= cost
                successful_purchases += 1
        
        print(f"成功买入 {successful_purchases} 只股票")
        print(f"剩余现金: {self.cash:,.2f}")
    
    def calculate_portfolio_value(self, day_data):
        """计算投资组合价值"""
        total_value = self.cash
        
        for symbol, shares in self.positions.items():
            if shares > 0:
                stock_data = day_data[day_data['symbol'] == symbol]
                if not stock_data.empty:
                    price = stock_data.iloc[0]['close_price']
                    total_value += shares * price
                # 如果股票当天没有数据，按上一次价格计算（简化处理）
        
        return total_value

def calculate_performance_metrics(portfolio_df):
    """计算回测表现指标"""
    if portfolio_df.empty:
        return None
    
    # 计算收益率
    portfolio_df['daily_return'] = portfolio_df['portfolio_value'].pct_change()
    
    # 基本指标
    start_value = portfolio_df['portfolio_value'].iloc[0]
    end_value = portfolio_df['portfolio_value'].iloc[-1]
    total_return = (end_value / start_value - 1) * 100
    
    # 年化收益率
    days = len(portfolio_df)
    annual_return = ((end_value / start_value) ** (252 / days) - 1) * 100
    
    # 最大回撤
    portfolio_df['cummax'] = portfolio_df['portfolio_value'].cummax()
    portfolio_df['drawdown'] = (portfolio_df['portfolio_value'] / portfolio_df['cummax'] - 1) * 100
    max_drawdown = portfolio_df['drawdown'].min()
    
    # 波动率
    daily_returns = portfolio_df['daily_return'].dropna()
    volatility = daily_returns.std() * np.sqrt(252) * 100
    
    # 夏普比率（假设无风险利率3%）
    risk_free_rate = 0.03
    excess_return = annual_return / 100 - risk_free_rate
    sharpe_ratio = excess_return / (volatility / 100) if volatility > 0 else 0
    
    # 胜率
    win_rate = (daily_returns > 0).sum() / len(daily_returns) * 100
    
    return {
        'start_value': start_value,
        'end_value': end_value,
        'total_return': total_return,
        'annual_return': annual_return,
        'max_drawdown': max_drawdown,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate,
        'trading_days': days
    }

def plot_results(portfolio_df):
    """绘制回测结果图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 组合价值曲线
    ax1.plot(portfolio_df['date'], portfolio_df['portfolio_value'])
    ax1.set_title('投资组合价值变化')
    ax1.set_ylabel('组合价值')
    ax1.grid(True)
    
    # 回撤曲线
    ax2.fill_between(portfolio_df['date'], portfolio_df['drawdown'], 0, alpha=0.3, color='red')
    ax2.set_title('回撤曲线')
    ax2.set_ylabel('回撤 (%)')
    ax2.grid(True)
    
    # 现金vs股票价值
    ax3.plot(portfolio_df['date'], portfolio_df['cash'], label='现金', alpha=0.7)
    ax3.plot(portfolio_df['date'], portfolio_df['stock_value'], label='股票价值', alpha=0.7)
    ax3.set_title('资产配置')
    ax3.set_ylabel('价值')
    ax3.legend()
    ax3.grid(True)
    
    # 持仓数量
    ax4.plot(portfolio_df['date'], portfolio_df['positions_count'])
    ax4.set_title('持仓股票数量')
    ax4.set_ylabel('股票数量')
    ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig('backtest_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=" * 60)
    print("小市值低市盈率策略回测")
    print("=" * 60)
    
    # 生成示例数据
    data = generate_sample_data('2025-01-01')
    
    if data.empty:
        print("没有数据，退出程序")
        return
    
    # 运行回测
    strategy = SmallCapStrategy(initial_capital=1000000)
    portfolio_df = strategy.run_backtest(data)
    
    # 计算表现指标
    metrics = calculate_performance_metrics(portfolio_df)
    
    if metrics:
        print("\n" + "=" * 60)
        print("回测结果:")
        print("=" * 60)
        print(f"初始资金: ¥{metrics['start_value']:,.2f}")
        print(f"最终资金: ¥{metrics['end_value']:,.2f}")
        print(f"总收益率: {metrics['total_return']:.2f}%")
        print(f"年化收益率: {metrics['annual_return']:.2f}%")
        print(f"最大回撤: {metrics['max_drawdown']:.2f}%")
        print(f"年化波动率: {metrics['volatility']:.2f}%")
        print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
        print(f"胜率: {metrics['win_rate']:.2f}%")
        print(f"交易天数: {metrics['trading_days']}")
        print("=" * 60)
        
        # 保存结果
        portfolio_df.to_csv('portfolio_performance.csv', index=False)
        print("详细结果已保存到 portfolio_performance.csv")
        
        # 绘制图表
        try:
            plot_results(portfolio_df)
            print("图表已保存到 backtest_results.png")
        except Exception as e:
            print(f"绘图失败: {e}")
    
    else:
        print("计算表现指标失败")

if __name__ == "__main__":
    main()
