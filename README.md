# 股票回测系统

基于MySQL数据库的股票量化回测系统，实现小市值低市盈率策略。

## 功能特点

- **数据源**: 从MySQL数据库读取真实股票数据
- **策略**: 小市值低市盈率选股策略
- **筛选条件**: 市盈率0-40（可配置）
- **排序条件**: 按总市值从小到大排序
- **调仓频率**: 每20个交易日调仓一次（可配置）
- **持仓数量**: 30只股票（可配置）
- **风险控制**: 等权重分配，保留现金缓冲

## 文件说明

- `basic_backtest.py` - 主回测程序
- `config.py` - 配置文件
- `table_definitions.txt` - 数据库表结构定义
- `stock_goldminer_bj.sql` - 数据库建表脚本

## 安装依赖

```bash
pip install mysql-connector-python
```

## 配置说明

### 1. 数据库配置 (config.py)

```python
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机
    'port': 3306,              # 端口
    'user': 'root',            # 用户名
    'password': 'your_password',  # 密码
    'database': 'stock_goldminer_bj',  # 数据库名
    'charset': 'utf8mb4'
}
```

### 2. 策略参数配置

```python
STRATEGY_CONFIG = {
    'initial_capital': 1000000,    # 初始资金（元）
    'rebalance_freq': 20,          # 调仓频率（交易日）
    'max_positions': 30,           # 最大持仓数量
    'pe_min': 0,                   # 市盈率最小值
    'pe_max': 40,                  # 市盈率最大值
    'cash_reserve_ratio': 0.02     # 现金保留比例
}
```

### 3. 时间配置

```python
TIME_CONFIG = {
    'start_date': '2024-01-01',    # 回测开始日期
    'end_date': None               # 结束日期，None表示到今天
}
```

## 数据库表结构

系统需要以下3个核心表：

1. **daily_basic** - 股票基础数据（收盘价、换手率等）
2. **daily_mktvalue** - 市值数据（总市值等）
3. **daily_valuation** - 估值数据（市盈率等）

详细表结构请参考 `table_definitions.txt`

## 使用方法

### 1. 准备数据库

```sql
-- 创建数据库
CREATE DATABASE stock_goldminer_bj;

-- 导入表结构
source stock_goldminer_bj.sql;

-- 导入股票数据（需要自行准备数据）
```

### 2. 修改配置

编辑 `config.py` 文件，设置正确的数据库连接参数。

### 3. 运行回测

```bash
python basic_backtest.py
```

## 输出结果

### 控制台输出

```
============================================================
小市值低市盈率策略回测 - 基于MySQL数据库
============================================================
回测期间: 2025-04-01 到 2025-01-09
筛选条件: PE 0-40
策略参数: 调仓频率20天, 持仓10只
成功加载 15420 条数据记录

调仓日期: 2025-04-01
买入详情:
  SH600001: 1000股 @ 10.50
  SH600002: 800股 @ 12.25
  ... 还有8只股票
成功买入 10 只股票

调仓日期: 2025-04-21
卖出详情:
  SH600001: 1000股 @ 11.20 盈亏: 700.00 (6.67%)
  SH600002: 800股 @ 11.80 盈亏: -360.00 (-2.94%)
  ... 还有8只股票
买入详情:
  SH600010: 900股 @ 8.50
  SH600011: 1100股 @ 7.80
  ... 还有8只股票
卖出 10 只股票，买入 10 只股票

============================================================
回测结果:
============================================================
初始资金: ¥1,000,000.00
最终资金: ¥1,156,789.23
总收益率: 15.68%
年化收益率: 15.23%
最大回撤: -8.45%
年化波动率: 18.76%
夏普比率: 0.65
胜率: 52.45%
交易天数: 245

============================================================
交易统计:
============================================================
总交易次数: 240
买入次数: 120
卖出次数: 120
总盈亏: ¥156,789.23
盈利交易: 68 次
亏损交易: 52 次
交易胜率: 56.67%
平均盈利: ¥3,245.67
最大盈利: ¥12,500.00
平均亏损: ¥-1,890.45
最大亏损: ¥-8,200.00
调仓次数: 12
============================================================
```

### CSV文件输出

系统会生成3个CSV文件：

#### 1. backtest_results.csv - 组合价值记录
包含每日的组合整体表现：
- 组合价值
- 现金余额
- 股票价值
- 持仓数量

#### 2. trading_records.csv - 详细交易记录
包含每笔买入和卖出交易：
- 交易日期和动作
- 股票代码和数量
- 买入价格和卖出价格
- 盈亏金额和百分比

#### 3. positions_summary.csv - 持仓汇总
按调仓日期汇总的持仓变化，便于分析每次调仓的整体效果

## 策略逻辑

1. **股票筛选**: 选择市盈率在0-40之间的股票
2. **排序选股**: 按总市值从小到大排序，选择前30只
3. **资金分配**: 等权重分配可用资金（保留2%现金）
4. **调仓频率**: 每20个交易日重新选股和调仓
5. **风险控制**: 分散持仓，现金缓冲

## 性能指标说明

- **总收益率**: (期末价值 - 期初价值) / 期初价值
- **年化收益率**: 按252个交易日年化计算
- **最大回撤**: 从最高点到最低点的最大跌幅
- **夏普比率**: 超额收益与波动率的比值
- **胜率**: 上涨交易日占比

## 注意事项

1. 确保数据库中有足够的历史数据
2. 数据质量直接影响回测结果的可靠性
3. 回测结果不代表未来表现
4. 建议在实盘前进行充分的参数优化和风险评估

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多技术指标筛选条件
- 实现动态调仓逻辑
- 增加风险管理模块
- 支持多因子选股模型
- 添加交易成本计算
