# 交易记录功能说明

## 功能概述

系统现在会详细记录每次调仓时的所有交易信息，包括：
- 每只股票的买入和卖出详情
- 买入价格和卖出价格
- 持仓数量
- 盈亏情况

## 输出文件

运行回测后，系统会生成以下3个CSV文件：

### 1. backtest_results.csv - 组合价值记录
记录每日的组合整体表现：
```csv
date,portfolio_value,cash,stock_value,positions_count
2025-04-01,1000000.00,20000.00,980000.00,10
2025-04-02,1005000.00,20000.00,985000.00,10
...
```

### 2. trading_records.csv - 详细交易记录
记录每笔买入和卖出交易：
```csv
date,action,symbol,shares,price,value,buy_price,profit_loss,profit_loss_pct
2025-04-01,买入,SH600001,1000,10.50,10500.00,10.50,0.00,0.00
2025-04-01,买入,SH600002,800,12.25,9800.00,12.25,0.00,0.00
2025-04-21,卖出,SH600001,1000,11.20,11200.00,10.50,700.00,6.67
2025-04-21,买入,SH600003,900,11.00,9900.00,11.00,0.00,0.00
...
```

字段说明：
- `date`: 交易日期
- `action`: 交易动作（买入/卖出）
- `symbol`: 股票代码
- `shares`: 交易股数
- `price`: 交易价格
- `value`: 交易金额
- `buy_price`: 买入价格（卖出时显示原买入价）
- `profit_loss`: 盈亏金额（仅卖出时有效）
- `profit_loss_pct`: 盈亏百分比（仅卖出时有效）

### 3. positions_summary.csv - 持仓汇总
按调仓日期汇总的持仓变化：
```csv
rebalance_date,action,symbol,shares,price,value,buy_price,profit_loss,profit_loss_pct
2025-04-01,买入,SH600001,1000,10.50,10500.00,10.50,0.00,0.00
2025-04-01,买入,SH600002,800,12.25,9800.00,12.25,0.00,0.00
2025-04-21,卖出,SH600001,1000,11.20,11200.00,10.50,700.00,6.67
2025-04-21,卖出,SH600002,800,11.80,9440.00,12.25,-360.00,-2.94
2025-04-21,买入,SH600003,900,11.00,9900.00,11.00,0.00,0.00
...
```

## 控制台输出增强

运行回测时，控制台会显示详细的调仓信息：

```
调仓日期: 2025-04-21

卖出详情:
  SH600001: 1000股 @ 11.20 盈亏: 700.00 (6.67%)
  SH600002: 800股 @ 11.80 盈亏: -360.00 (-2.94%)
  SH600003: 1200股 @ 9.50 盈亏: 240.00 (2.11%)
  ... 还有7只股票

买入详情:
  SH600010: 900股 @ 8.50
  SH600011: 1100股 @ 7.80
  SH600012: 800股 @ 10.20
  ... 还有7只股票

卖出 10 只股票，买入 10 只股票
剩余现金: 19,850.00
```

## 交易统计信息

回测结束后会显示详细的交易统计：

```
============================================================
交易统计:
============================================================
总交易次数: 240
买入次数: 120
卖出次数: 120
总盈亏: ¥156,789.23
盈利交易: 68 次
亏损交易: 52 次
交易胜率: 56.67%
平均盈利: ¥3,245.67
最大盈利: ¥12,500.00
平均亏损: ¥-1,890.45
最大亏损: ¥-8,200.00
调仓次数: 12
============================================================
```

## 配置选项

在 `config.py` 中可以配置输出文件名：

```python
OUTPUT_CONFIG = {
    'save_csv': True,
    'csv_filename': 'backtest_results.csv',           # 组合价值文件
    'trading_records_filename': 'trading_records.csv', # 交易记录文件
    'positions_summary_filename': 'positions_summary.csv', # 持仓汇总文件
    'show_progress': True,
    'progress_interval': 10
}
```

## 数据分析建议

### 1. 交易表现分析
使用 `trading_records.csv` 分析：
- 哪些股票盈利最多
- 持仓时间与收益的关系
- 不同市场环境下的交易表现

### 2. 持仓分析
使用 `positions_summary.csv` 分析：
- 每次调仓的整体盈亏
- 调仓频率对收益的影响
- 持仓集中度变化

### 3. 风险分析
- 单笔最大亏损
- 连续亏损次数
- 盈亏比分析

## Excel分析示例

### 透视表分析
1. 按月份统计盈亏
2. 按股票代码统计交易次数
3. 按盈亏区间分布统计

### 图表分析
1. 累计盈亏曲线
2. 月度盈亏柱状图
3. 胜率变化趋势

## 注意事项

1. **数据完整性**: 确保买入和卖出记录配对完整
2. **价格准确性**: 交易价格基于数据库中的收盘价
3. **手续费**: 当前版本未计算交易手续费和印花税
4. **滑点**: 未考虑实际交易中的滑点成本

## 后续优化建议

1. **成本计算**: 加入交易成本计算
2. **分红处理**: 考虑分红对收益的影响
3. **停牌处理**: 优化停牌股票的处理逻辑
4. **实时监控**: 添加实时持仓监控功能
