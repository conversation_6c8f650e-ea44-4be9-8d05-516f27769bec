/*
SQLyog Community v13.3.0 (64 bit)
MySQL - 8.0.33 : Database - stock_goldminer_bj
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`stock_goldminer_bj` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `stock_goldminer_bj`;

/*Table structure for table `daily_basic` */

DROP TABLE IF EXISTS `daily_basic`;

CREATE TABLE `daily_basic` (
  `symbol` varchar(20) NOT NULL,
  `trade_date` varchar(20) NOT NULL,
  `h_shr_unl` double DEFAULT NULL,
  `a_shr_unl` double DEFAULT NULL,
  `ttl_shr` double DEFAULT NULL,
  `ttl_shr_ltd` double DEFAULT NULL,
  `turnrate` double DEFAULT NULL,
  `ttl_shr_unl` double DEFAULT NULL,
  `circ_shr` double DEFAULT NULL,
  `tclose` double DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`symbol`,`trade_date`),
  KEY `idx_trade_date_turnrate` (`trade_date`,`turnrate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `daily_mktvalue` */

DROP TABLE IF EXISTS `daily_mktvalue`;

CREATE TABLE `daily_mktvalue` (
  `symbol` varchar(20) NOT NULL,
  `trade_date` varchar(20) NOT NULL,
  `tot_mv_csrc` double DEFAULT NULL,
  `ev` double DEFAULT NULL,
  `equity_value` double DEFAULT NULL,
  `a_mv_ex_ltd` double DEFAULT NULL,
  `tot_mv` double DEFAULT NULL,
  `ev_ebitda` double DEFAULT NULL,
  `a_mv` double DEFAULT NULL,
  `b_mv_ex_ltd` double DEFAULT NULL,
  `b_mv` double DEFAULT NULL,
  `ev_ex_curr` double DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`symbol`,`trade_date`),
  KEY `idx_tot_mv_trade_date` (`tot_mv`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `daily_valuation` */

DROP TABLE IF EXISTS `daily_valuation`;

CREATE TABLE `daily_valuation` (
  `symbol` varchar(20) NOT NULL,
  `trade_date` varchar(20) NOT NULL,
  `pe_ttm` double DEFAULT NULL,
  `ps_ttm` double DEFAULT NULL,
  `dy_ttm` double DEFAULT NULL,
  `dy_lfy` double DEFAULT NULL,
  `ps_mrq` double DEFAULT NULL,
  `peg_mrq` double DEFAULT NULL,
  `pb_lyr` double DEFAULT NULL,
  `pe_ttm_cut` double DEFAULT NULL,
  `ps_lyr` double DEFAULT NULL,
  `pb_mrq` double DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`symbol`,`trade_date`),
  KEY `idx_trade_date_pe_dy` (`trade_date`,`pe_ttm`,`dy_ttm`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `finance_deriv` */

DROP TABLE IF EXISTS `finance_deriv`;

CREATE TABLE `finance_deriv` (
  `symbol` varchar(20) NOT NULL,
  `pub_date` varchar(20) DEFAULT NULL,
  `rpt_date` varchar(20) NOT NULL,
  `rpt_type` bigint DEFAULT NULL,
  `data_type` bigint DEFAULT NULL,
  `sale_gpm` double DEFAULT NULL,
  `roa` double DEFAULT NULL,
  `roe` double DEFAULT NULL,
  `roe_cut` double DEFAULT NULL,
  `ast_liab_rate` double DEFAULT NULL,
  `sale_npm` double DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`symbol`,`rpt_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*Table structure for table `fundamentals_income` */

DROP TABLE IF EXISTS `fundamentals_income`;

CREATE TABLE `fundamentals_income` (
  `symbol` varchar(20) NOT NULL,
  `pub_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `rpt_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `inc_oper` double DEFAULT NULL,
  `rpt_type` bigint DEFAULT NULL,
  `data_type` bigint DEFAULT NULL,
  `net_prof` double DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`symbol`,`rpt_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
