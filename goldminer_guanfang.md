# 掘金量化：股票财务数据及基础数据函数(免费)

# 股票财务数据及基础数据函数(免费)

python 股票与指数数据 API 包含在 gm3.0.148 版本及以上版本

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-index-constituents-%E6%9F%A5%E8%AF%A2%E6%8C%87%E6%95%B0%E6%88%90%E5%88%86%E8%82%A1)​`stk_get_index_constituents`​ - 查询指数成分股

查询指定指数在最新交易日的成分股和权重(中证系列指数，因版权不提供成分股权重，weight\=0)

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_index_constituents(index, trade_date=None)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| ----------------------------------------------|
|index|str|指数代码|Y|无|必填，只能输入一个指数，如：`'SHSE.000905'`​|
|trade\_date|str|交易日期|N|None|交易日期，%Y-%m-%d 格式， 默认 `None` ​为最新交易日|

**返回值：**​`dataframe`​

|字段名|类型|中文名称|说明|
| ------------------| -----| ----------| --------------------------------------------------------|
|index|str|指数代码|查询成分股的指数代码|
|symbol|str|成分股代码|exchange.sec\_id|
|weight|float|成分股权重|成分股 symbol 对应的指数权重 (中证系列指数不支持该字段）|
|trade\_date|str|交易日期|最新交易日，%Y-%m-%d 格式|
|market\_value\_total|float|总市值|单位：亿元|
|market\_value\_circ|float|流通市值|单位：亿元|

**示例：**

```python
stk_get_index_constituents(index='SHSE.000300')
 
        复制成功
  
```

**输出：**

```python
          index       symbol  weight  trade_date  market_value_total  market_value_circ
0    SHSE.000300  SHSE.600519    0.05  2023-04-18            22083.96           22083.96
1    SHSE.000300  SZSE.300750    0.03  2023-04-18             9989.35            8822.91
2    SHSE.000300  SHSE.601318    0.03  2023-04-18             8887.85            5266.84
3    SHSE.000300  SHSE.600036    0.02  2023-04-18             8998.44            7360.41
4    SHSE.000300  SZSE.000858    0.02  2023-04-18             6921.68            6921.39
5    SHSE.000300  SZSE.000333    0.01  2023-04-18             3972.72            3891.18
6    SHSE.000300  SHSE.601166    0.01  2023-04-18             3616.80            3616.80
7    SHSE.000300  SHSE.600900    0.01  2023-04-18             5030.92            4834.92
8    SHSE.000300  SHSE.601012    0.01  2023-04-18             3033.36            3031.97
9    SHSE.000300  SZSE.300059    0.01  2023-04-18             2859.02            2399.14
10   SHSE.000300  SZSE.002594    0.01  2023-04-18             7248.75            2900.26...
 
        复制成功
  
```

**注意：**

**1.**  数据日频更新，在交易日约 20 点更新当日数据。如果当日数据尚未更新，调用时不指定 `trade_date` ​会返回前一交易日的成分数据，调用时指定 `trade_date` ​为当日会返回空 dataframe。

**2.**  `trade_date` ​输入非交易日，会返回空 dataframe。`trade_date` ​出入的日期格式错误，会报错。

**3.**  指数列表[参考](https://www.myquant.cn/docs2/docs/%E6%8C%87%E6%95%B0.html#%E6%8C%87%E6%95%B0%E6%88%90%E5%88%86%E8%82%A1)

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-balance-pt-%E6%9F%A5%E8%AF%A2%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_balance_pt`​ - 查询资产负债表截面数据（多标的）

查询指定日期截面的股票所属上市公司的资产负债表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_balance_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------| -----------| --------| ----| ------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[资产负债表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#balance)**|

**示例：**

```python
stk_get_fundamentals_balance_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='fix_ast', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date        fix_ast  data_type  rpt_type
0  SZSE.000001  2022-10-25  2022-09-30 10975000000.00        102         9
1  SHSE.600000  2022-10-29  2022-09-30 42563000000.00        102         9
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期 `date` ​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**资产负债表**

|字段名|类型|中文名称|量纲|说明|
| ---------------------| -----| ----------------------------------------------------------| ----| ----------------|
|***流动资产(资产)***|||||
|cash\_bal\_cb|float|现金及存放中央银行款项|元|银行|
|dpst\_ob|float|存放同业款项|元|银行|
|mny\_cptl|float|货币资金|元||
|cust\_cred\_dpst|float|客户信用资金存款|元|证券|
|cust\_dpst|float|客户资金存款|元|证券|
|pm|float|贵金属|元|银行|
|bal\_clr|float|结算备付金|元||
|cust\_rsv|float|客户备付金|元|证券|
|ln\_to\_ob|float|拆出资金|元||
|fair\_val\_fin\_ast|float|以公允价值计量且其变动计入当期损益的金融资产|元||
|ppay|float|预付款项|元||
|fin\_out|float|融出资金|元||
|trd\_fin\_ast|float|交易性金融资产|元||
|deriv\_fin\_ast|float|衍生金融资产|元||
|note\_acct\_rcv|float|应收票据及应收账款|元||
|note\_rcv|float|应收票据|元||
|acct\_rcv|float|应收账款|元||
|acct\_rcv\_fin|float|应收款项融资|元||
|int\_rcv|float|应收利息|元||
|dvd\_rcv|float|应收股利|元||
|oth\_rcv|float|其他应收款|元||
|in\_prem\_rcv|float|应收保费|元||
|rin\_acct\_rcv|float|应收分保账款|元||
|rin\_rsv\_rcv|float|应收分保合同准备金|元|保险|
|rcv\_un\_prem\_rin\_rsv|float|应收分保未到期责任准备金|元||
|rcv\_clm\_rin\_rsv|float|应收分保未决赔偿准备金|元|保险|
|rcv\_li\_rin\_rsv|float|应收分保寿险责任准备金|元|保险|
|rcv\_lt\_hi\_rin\_rsv|float|应收分保长期健康险责任准备金|元|保险|
|ph\_plge\_ln|float|保户质押贷款|元|保险|
|ttl\_oth\_rcv|float|其他应收款合计|元||
|rfd\_dpst|float|存出保证金|元|证券、保险|
|term\_dpst|float|定期存款|元|保险|
|pur\_resell\_fin|float|买入返售金融资产|元||
|aval\_sale\_fin|float|可供出售金融资产|元||
|htm\_inv|float|持有至到期投资|元||
|hold\_for\_sale|float|持有待售资产|元||
|acct\_rcv\_inv|float|应收款项类投资|元|保险|
|invt|float|存货|元||
|contr\_ast|float|合同资产|元||
|ncur\_ast\_one\_y|float|一年内到期的非流动资产|元||
|oth\_cur\_ast|float|其他流动资产|元||
|cur\_ast\_oth\_item|float|流动资产其他项目|元||
|ttl\_cur\_ast|float|流动资产合计|元||
|***非流动资产(资产)***|||||
|loan\_adv|float|发放委托贷款及垫款|元||
|cred\_inv|float|债权投资|元||
|oth\_cred\_inv|float|其他债权投资|元||
|lt\_rcv|float|长期应收款|元||
|lt\_eqy\_inv|float|长期股权投资|元||
|oth\_eqy\_inv|float|其他权益工具投资|元||
|rfd\_cap\_guar\_dpst|float|存出资本保证金|元|保险|
|oth\_ncur\_fin\_ast|float|其他非流动金融资产|元||
|amor\_cos\_fin\_ast\_ncur|float|以摊余成本计量的金融资产（非流动）|元||
|fair\_val\_oth\_inc\_ncur|float|以公允价值计量且其变动计入其他综合收益的金融资产（非流动）|元||
|inv\_prop|float|投资性房地产|元||
|fix\_ast|float|固定资产|元||
|const\_prog|float|在建工程|元||
|const\_matl|float|工程物资|元||
|fix\_ast\_dlpl|float|固定资产清理|元||
|cptl\_bio\_ast|float|生产性生物资产|元||
|oil\_gas\_ast|float|油气资产|元||
|rig\_ast|float|使用权资产|元||
|intg\_ast|float|无形资产|元||
|trd\_seat\_fee|float|交易席位费|元|证券|
|dev\_exp|float|开发支出|元||
|gw|float|商誉|元||
|lt\_ppay\_exp|float|长期待摊费用|元||
|dfr\_tax\_ast|float|递延所得税资产|元||
|oth\_ncur\_ast|float|其他非流动资产|元||
|ncur\_ast\_oth\_item|float|非流动资产其他项目|元||
|ttl\_ncur\_ast|float|非流动资产合计|元||
|oth\_ast|float|其他资产|元|银行、证券、保险|
|ast\_oth\_item|float|资产其他项目|元||
|ind\_acct\_ast|float|独立账户资产|元|保险|
|ttl\_ast|float|资产总计|元||
|***流动负债(负债)***|||||
|brw\_cb|float|向中央银行借款|元||
|dpst\_ob\_fin\_inst|float|同业和其他金融机构存放款项|元|银行、保险|
|ln\_fm\_ob|float|拆入资金|元||
|fair\_val\_fin\_liab|float|以公允价值计量且其变动计入当期损益的金融负债|元||
|sht\_ln|float|短期借款|元||
|adv\_acct|float|预收款项|元||
|contr\_liab|float|合同负债|元||
|trd\_fin\_liab|float|交易性金融负债|元||
|deriv\_fin\_liab|float|衍生金融负债|元||
|sell\_repo\_ast|float|卖出回购金融资产款|元||
|cust\_bnk\_dpst|float|吸收存款|元|银行、保险|
|dpst\_cb\_note\_pay|float|存款证及应付票据|元|银行|
|dpst\_cb|float|存款证|元|银行|
|acct\_rcv\_adv|float|预收账款|元|保险|
|in\_prem\_rcv\_adv|float|预收保费|元|保险|
|fee\_pay|float|应付手续费及佣金|元||
|note\_acct\_pay|float|应付票据及应付账款|元||
|stlf\_pay|float|应付短期融资款|元||
|note\_pay|float|应付票据|元||
|acct\_pay|float|应付账款|元||
|rin\_acct\_pay|float|应付分保账款|元||
|emp\_comp\_pay|float|应付职工薪酬|元||
|tax\_pay|float|应交税费|元||
|int\_pay|float|应付利息|元||
|dvd\_pay|float|应付股利|元||
|ph\_dvd\_pay|float|应付保单红利|元|保险|
|indem\_pay|float|应付赔付款|元|保险|
|oth\_pay|float|其他应付款|元||
|ttl\_oth\_pay|float|其他应付款合计|元||
|ph\_dpst\_inv|float|保户储金及投资款|元|保险|
|in\_contr\_rsv|float|保险合同准备金|元|保险|
|un\_prem\_rsv|float|未到期责任准备金|元|保险|
|clm\_rin\_rsv|float|未决赔款准备金|元|保险|
|li\_liab\_rsv|float|寿险责任准备金|元|保险|
|lt\_hi\_liab\_rsv|float|长期健康险责任准备金|元|保险|
|cust\_bnk\_dpst\_fin|float|吸收存款及同业存放|元||
|inter\_pay|float|内部应付款|元||
|agy\_secu\_trd|float|代理买卖证券款|元||
|agy\_secu\_uw|float|代理承销证券款|元||
|sht\_bnd\_pay|float|应付短期债券|元||
|est\_cur\_liab|float|预计流动负债|元||
|liab\_hold\_for\_sale|float|持有待售负债|元||
|ncur\_liab\_one\_y|float|一年内到期的非流动负债|元||
|oth\_cur\_liab|float|其他流动负债|元||
|cur\_liab\_oth\_item|float|流动负债其他项目|元||
|ttl\_cur\_liab|float|流动负债合计|元||
|***非流动负债（负债）***|||||
|lt\_ln|float|长期借款|元||
|lt\_pay|float|长期应付款|元||
|leas\_liab|float|租赁负债|||
|dfr\_inc|float|递延收益|元||
|dfr\_tax\_liab|float|递延所得税负债|元||
|bnd\_pay|float|应付债券|元||
|bnd\_pay\_pbd|float|其中:永续债|元||
|bnd\_pay\_pfd|float|其中:优先股|元||
|oth\_ncur\_liab|float|其他非流动负债|元||
|spcl\_pay|float|专项应付款|元||
|ncur\_liab\_oth\_item|float|非流动负债其他项目|元||
|lt\_emp\_comp\_pay|float|长期应付职工薪酬|元||
|est\_liab|float|预计负债|元||
|oth\_liab|float|其他负债|元|银行、证券、保险|
|liab\_oth\_item|float|负债其他项目|元|银行、证券、保险|
|ttl\_ncur\_liab|float|非流动负债合计|元||
|ind\_acct\_liab|float|独立账户负债|元|保险|
|ttl\_liab|float|负债合计|元||
|***所有者权益(或股东权益)***|||||
|paid\_in\_cptl|float|实收资本（或股本）|元||
|oth\_eqy|float|其他权益工具|元||
|oth\_eqy\_pfd|float|其中:优先股|元||
|oth\_eqy\_pbd|float|其中:永续债|元||
|oth\_eqy\_oth|float|其中:其他权益工具|元||
|cptl\_rsv|float|资本公积|元||
|treas\_shr|float|库存股|元||
|oth\_comp\_inc|float|其他综合收益|元||
|spcl\_rsv|float|专项储备|元||
|sur\_rsv|float|盈余公积|元||
|rsv\_ord\_rsk|float|一般风险准备|元||
|trd\_risk\_rsv|float|交易风险准备|元|证券|
|ret\_prof|float|未分配利润|元||
|sugg\_dvd|float|建议分派股利|元|银行|
|eqy\_pcom\_oth\_item|float|归属于母公司股东权益其他项目|元||
|ttl\_eqy\_pcom|float|归属于母公司股东权益合计|元||
|min\_sheqy|float|少数股东权益|元||
|sheqy\_oth\_item|float|股东权益其他项目|元||
|ttl\_eqy|float|股东权益合计|元||
|ttl\_liab\_eqy|float|负债和股东权益合计|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-cashflow-pt-%E6%9F%A5%E8%AF%A2%E7%8E%B0%E9%87%91%E6%B5%81%E9%87%8F%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_cashflow_pt`​ - 查询现金流量表截面数据（多标的）

查询指定日期截面的股票所属上市公司的现金流量表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_cashflow_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------| -----------| --------| ----| ------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />2-第二季度<br />3-第三季度<br />4-第四季度<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报, 2-第二季度, 3-第三季度, 4-第四季度|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[现金流量表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#cashflow)**|

**示例：**

```python
stk_get_fundamentals_cashflow_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='cash_pay_fee', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  cash_pay_fee
0  SZSE.000001  2022-10-25  2022-09-30         9        102           NaN
1  SHSE.600000  2022-10-29  2022-09-30         9        102 7261000000.00
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期 `date` ​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**现金流量表**

|字段名|类型|中文名称|量纲|说明|
| ------------------------| -----| ---------------------------------------------------| ----| ----------------|
|***一、经营活动产生的现金流量***|||||
|cash\_rcv\_sale|float|销售商品、提供劳务收到的现金|元||
|net\_incr\_cust\_dpst\_ob|float|客户存款和同业存放款项净增加额|元||
|net\_incr\_cust\_dpst|float|客户存款净增加额|元|银行|
|net\_incr\_dpst\_ob|float|同业及其他金融机构存放款项净增加额|元|银行|
|net\_incr\_brw\_cb|float|向中央银行借款净增加额|元||
|net\_incr\_ln\_fm\_oth|float|向其他金融机构拆入资金净增加额|元||
|cash\_rcv\_orig\_in|float|收到原保险合同保费取得的现金|元||
|net\_cash\_rcv\_rin\_biz|float|收到再保险业务现金净额|元||
|net\_incr\_ph\_dpst\_inv|float|保户储金及投资款净增加额|元||
|net\_decrdpst\_cb\_ob|float|存放中央银行和同业款项及其他金融机构净减少额|元|银行、保险|
|net\_decr\_cb|float|存放中央银行款项净减少额|元|银行|
|net\_decr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净减少额|元|银行|
|net\_cert\_dpst|float|存款证净额|元|银行|
|net\_decr\_trd\_fin|float|交易性金融资产净减少额|元|银行|
|net\_incr\_trd\_liab|float|交易性金融负债净增加额|元|银行|
|cash\_rcv\_int\_fee|float|收取利息、手续费及佣金的现金|元||
|cash\_rcv\_int|float|其中：收取利息的现金|元|银行|
|cash\_rcv\_fee|float|收取手续费及佣金的现金|元|银行|
|net\_incr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净增加额|元|银行|
|net\_incr\_ln\_fm|float|拆入资金净增加额|元||
|net\_incr\_sell\_repo|float|卖出回购金融资产款净增加额|元|银行 保险|
|net\_decr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净减少额|元|银行|
|net\_decr\_ln\_cptl|float|拆出资金净减少额|元|银行、保险|
|net\_dect\_pur\_resell|float|买入返售金融资产净减少额|元|银行、保险|
|net\_incr\_repo|float|回购业务资金净增加额|元||
|net\_decr\_repo|float|回购业务资金净减少额|元|证券|
|tax\_rbt\_rcv|float|收到的税费返还|元||
|net\_cash\_rcv\_trd|float|收到交易性金融资产现金净额|元|保险|
|cash\_rcv\_oth\_oper|float|收到其他与经营活动有关的现金|元||
|net\_cash\_agy\_secu\_trd|float|代理买卖证券收到的现金净额|元|证券|
|cash\_rcv\_pur\_resell|float|买入返售金融资产收到的现金|元|证券、保险|
|net\_cash\_agy\_secu\_uw|float|代理承销证券收到的现金净额|元|证券|
|cash\_rcv\_dspl\_debt|float|处置抵债资产收到的现金|元|银行|
|canc\_loan\_rcv|float|收回的已于以前年度核销的贷款|元|银行|
|cf\_in\_oper|float|经营活动现金流入小计|元||
|cash\_pur\_gds\_svc|float|购买商品、接受劳务支付的现金|元||
|net\_incr\_ln\_adv\_cust|float|客户贷款及垫款净增加额|元||
|net\_decr\_brw\_cb|float|向中央银行借款净减少额|元|银行|
|net\_incr\_dpst\_cb\_ob|float|存放中央银行和同业款项净增加额|元||
|net\_incr\_cb|float|存放中央银行款项净增加额|元|银行|
|net\_incr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净增加额|元|银行|
|net\_decr\_dpst\_ob|float|同业及其他机构存放款减少净额|元|银行|
|net\_decr\_issu\_cert\_dpst|float|已发行存款证净减少额|元|银行|
|net\_incr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净增加额|元|银行|
|net\_incr\_ln\_to|float|拆出资金净增加额|元|银行、保险|
|net\_incr\_pur\_resell|float|买入返售金融资产净增加额|元|银行、保险|
|net\_decr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净减少额|元|银行|
|net\_decr\_ln\_fm|float|拆入资金净减少额|元|银行、证券|
|net\_decr\_sell\_repo|float|卖出回购金融资产净减少额|元|银行、保险|
|net\_incr\_trd\_fin|float|交易性金融资产净增加额|元|银行|
|net\_decr\_trd\_liab|float|交易性金融负债净减少额|元|银行|
|cash\_pay\_indem\_orig|float|支付原保险合同赔付款项的现金|元||
|net\_cash\_pay\_rin\_biz|float|支付再保险业务现金净额|元|保险|
|cash\_pay\_int\_fee|float|支付利息、手续费及佣金的现金|元||
|cash\_pay\_int|float|其中：支付利息的现金|元|银行|
|cash\_pay\_fee|float|支付手续费及佣金的现金|元|银行|
|ph\_dvd\_pay|float|支付保单红利的现金|元||
|net\_decr\_ph\_dpst\_inv|float|保户储金及投资款净减少额|元|保险|
|cash\_pay\_emp|float|支付给职工以及为职工支付的现金|||
|cash\_pay\_tax|float|支付的各项税费|元||
|net\_cash\_pay\_trd|float|支付交易性金融资产现金净额|元|保险|
|cash\_pay\_oth\_oper|float|支付其他与经营活动有关的现金|元||
|net\_incr\_dspl\_trd\_fin|float|处置交易性金融资产净增加额|元||
|cash\_pay\_fin\_leas|float|购买融资租赁资产支付的现金|元|银行|
|net\_decr\_agy\_secu\_pay|float|代理买卖证券支付的现金净额（净减少额）|元|证券|
|net\_decr\_dspl\_trd\_fin|float|处置交易性金融资产的净减少额|元|证券|
|cf\_out\_oper|float|经营活动现金流出小计|元||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|***二、投资活动产生的现金流量：***|||||
|cash\_rcv\_sale\_inv|float|收回投资收到的现金|元||
|inv\_inc\_rcv|float|取得投资收益收到的现金|元||
|cash\_rcv\_dvd\_prof|float|分得股利或利润所收到的现金|元|银行|
|cash\_rcv\_dspl\_ast|float|处置固定资产、无形资产和其他长期资产收回的现金净额|元||
|cash\_rcv\_dspl\_sub\_oth|float|处置子公司及其他营业单位收到的现金净额|元||
|cash\_rcv\_oth\_inv|float|收到其他与投资活动有关的现金|元||
|cf\_in\_inv|float|投资活动现金流入小计|元||
|pur\_fix\_intg\_ast|float|购建固定资产、无形资产和其他长期资产支付的现金|元||
|cash\_out\_dspl\_sub\_oth|float|处置子公司及其他营业单位流出的现金净额|元|保险|
|cash\_pay\_inv|float|投资支付的现金|元||
|net\_incr\_ph\_plge\_ln|float|保户质押贷款净增加额|元|保险|
|add\_cash\_pled\_dpst|float|增加质押和定期存款所支付的现金|元||
|net\_incr\_plge\_ln|float|质押贷款净增加额|元||
|net\_cash\_get\_sub|float|取得子公司及其他营业单位支付的现金净额|元||
|net\_pay\_pur\_resell|float|支付买入返售金融资产现金净额|元|证券、保险|
|cash\_pay\_oth\_inv|float|支付其他与投资活动有关的现金|元||
|cf\_out\_inv|float|投资活动现金流出小计|||
|net\_cf\_inv|float|投资活动产生的现金流量净额|元||
|***三、筹资活动产生的现金流量：***|||||
|cash\_rcv\_cptl|float|吸收投资收到的现金|元||
|sub\_rcv\_ms\_inv|float|其中：子公司吸收少数股东投资收到的现金|元||
|brw\_rcv|float|取得借款收到的现金|元||
|cash\_rcv\_bnd\_iss|float|发行债券收到的现金|元||
|net\_cash\_rcv\_sell\_repo|float|收到卖出回购金融资产款现金净额|元|保险|
|cash\_rcv\_oth\_fin|float|收到其他与筹资活动有关的现金|元||
|issu\_cert\_dpst|float|发行存款证|元|银行|
|cf\_in\_fin\_oth|float|筹资活动现金流入其他项目|元||
|cf\_in\_fin|float|筹资活动现金流入小计|元||
|cash\_rpay\_brw|float|偿还债务支付的现金|元||
|cash\_pay\_bnd\_int|float|偿付债券利息支付的现金|元|银行|
|cash\_pay\_dvd\_int|float|分配股利、利润或偿付利息支付的现金|元||
|sub\_pay\_dvd\_prof|float|其中：子公司支付给少数股东的股利、利润|元||
|cash\_pay\_oth\_fin|float|支付其他与筹资活动有关的现金|元||
|net\_cash\_pay\_sell\_repo|float|支付卖出回购金融资产款现金净额|元|保险|
|cf\_out\_fin|float|筹资活动现金流出小计|元||
|net\_cf\_fin|float|筹资活动产生的现金流量净额|元||
|efct\_er\_chg\_cash|float|四、汇率变动对现金及现金等价物的影响|元||
|net\_incr\_cash\_eq|float|五、现金及现金等价物净增加额|元||
|cash\_cash\_eq\_bgn|float|加：期初现金及现金等价物余额|元||
|cash\_cash\_eq\_end|float|六、期末现金及现金等价物余额|元||
|***补充资料 1．将净利润调节为经营活动现金流量：***|||||
|net\_prof|float|净利润|元||
|ast\_impr|float|资产减值准备|元||
|accr\_prvs\_ln\_impa|float|计提贷款减值准备|元|银行|
|accr\_prvs\_oth\_impa|float|计提其他资产减值准备|元|银行|
|accr\_prem\_rsv|float|提取的保险责任准备金|元|保险|
|accr\_unearn\_prem\_rsv|float|提取的未到期的责任准备金|元|保险|
|defr\_fix\_prop|float|固定资产和投资性房地产折旧|元||
|depr\_oga\_cba|float|其中:固定资产折旧、油气资产折耗、生产性生物资产折旧|元||
|amor\_intg\_ast\_lt\_exp|float|无形资产及长期待摊费用等摊销|元|银行、证券、保险|
|amort\_intg\_ast|float|无形资产摊销|元||
|amort\_lt\_exp\_ppay|float|长期待摊费用摊销|元||
|dspl\_ast\_loss|float|处置固定资产、无形资产和其他长期资产的损失|元||
|fair\_val\_chg\_loss|float|固定资产报废损失|元||
|fv\_chg\_loss|float|公允价值变动损失|元||
|dfa|float|固定资产折旧|元|银行|
|fin\_exp|float|财务费用|元||
|inv\_loss|float|投资损失|元||
|exchg\_loss|float|汇兑损失|元|银行、证券、保险|
|dest\_incr|float|存款的增加|元|银行|
|loan\_decr|float|贷款的减少|元|银行|
|cash\_pay\_bnd\_int\_iss|float|发行债券利息支出|元|银行|
|dfr\_tax|float|递延所得税|元||
|dfr\_tax\_ast\_decr|float|其中:递延所得税资产减少|元||
|dfr\_tax\_liab\_incr|float|递延所得税负债增加|元||
|invt\_decr|float|存货的减少|元||
|decr\_rcv\_oper|float|经营性应收项目的减少|元||
|incr\_pay\_oper|float|经营性应付项目的增加|元||
|oth|float|其他|元||
|cash\_end|float|现金的期末余额|元||
|cash\_bgn|float|减：现金的期初余额|元||
|cash\_eq\_end|float|加:现金等价物的期末余额|元||
|cash\_eq\_bgn|float|减:现金等价物的期初余额|元||
|cred\_impr\_loss|float|信用减值损失|元||
|est\_liab\_add|float|预计负债的增加|元||
|dr\_cnv\_cptl|float|债务转为资本|元||
|cptl\_bnd\_expr\_one\_y|float|一年内到期的可转换公司债券|元||
|fin\_ls\_fix\_ast|float|融资租入固定资产|元||
|amort\_dfr\_inc|float|递延收益摊销|元||
|depr\_inv\_prop|float|投资性房地产折旧|元||
|trd\_fin\_decr|float|交易性金融资产的减少|元|证券、保险|
|im\_net\_cf\_oper|float|间接法-经营活动产生的现金流量净额|元||
|im\_net\_incr\_cash\_eq|float|间接法-现金及现金等价物净增加额|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-income-pt-%E6%9F%A5%E8%AF%A2%E5%88%A9%E6%B6%A6%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_income_pt`​ - 查询利润表截面数据（多标的）

查询指定日期截面的股票所属上市公司的利润表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_income_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------| -----------| --------| ----| ------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />2-第二季度<br />3-第三季度<br />4-第四季度<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报, 2-第二季度, 3-第三季度, 4-第四季度|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[利润表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#income)**|

**示例：**

```python
stk_get_fundamentals_income_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='inc_oper', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type        inc_oper
0  SZSE.000001  2022-10-25  2022-09-30         9        102 138265000000.00
1  SHSE.600000  2022-10-29  2022-09-30         9        102 143680000000.00
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期 `date` ​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**利润表**

|字段名|类型|中文名称|量纲|说明|
| ----------------------| -----| ------------------------------------| ----| ----------------|
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|net\_inc\_int|float|利息净收入|元|证券、银行、保险|
|exp\_int|float|利息支出|元||
|net\_inc\_fee\_comm|float|手续费及佣金净收入|元|证券、银行|
|inc\_rin\_prem|float|其中：分保费收入|元|保险|
|net\_inc\_secu\_agy|float|其中:代理买卖证券业务净收入|元|证券|
|inc\_fee\_comm|float|手续费及佣金收入|元||
|in\_prem\_earn|float|已赚保费|元|保险|
|inc\_in\_biz|float|其中:保险业务收入|元|保险|
|rin\_prem\_cede|float|分出保费|元|保险|
|unear\_prem\_rsv|float|提取未到期责任准备金|元|保险|
|net\_inc\_uw|float|证券承销业务净收入|元|证券|
|net\_inc\_cust\_ast\_mgmt|float|受托客户资产管理业务净收入|元|证券|
|inc\_fx|float|汇兑收益|元||
|inc\_other\_oper|float|其他业务收入|元||
|inc\_oper\_balance|float|营业收入平衡项目|元||
|ttl\_inc\_oper\_other|float|营业总收入其他项目|元||
|ttl\_cost\_oper|float|营业总成本|元||
|cost\_oper|float|营业成本|元||
|exp\_oper|float|营业支出|元|证券、银行、保险|
|biz\_tax\_sur|float|营业税金及附加|元||
|exp\_sell|float|销售费用|元||
|exp\_adm|float|管理费用|元||
|exp\_rd|float|研发费用|元||
|exp\_fin|float|财务费用|元||
|int\_fee|float|其中:利息费用|元||
|inc\_int|float|利息收入|元||
|exp\_oper\_adm|float|业务及管理费|元|证券、银行、保险|
|exp\_rin|float|减:摊回分保费用|元|保险|
|rfd\_prem|float|退保金|元|保险|
|comp\_pay|float|赔付支出|元|保险|
|rin\_clm\_pay|float|减:摊回赔付支出|元|保险|
|draw\_insur\_liab|float|提取保险责任准备金|元|保险|
|amor\_insur\_liab|float|减:摊回保险责任准备金|元|保险|
|exp\_ph\_dvd|float|保单红利支出|元|保险|
|exp\_fee\_comm|float|手续费及佣金支出|元||
|other\_oper\_cost|float|其他业务成本|元||
|oper\_exp\_balance|float|营业支出平衡项目|元|证券、银行、保险|
|exp\_oper\_other|float|营业支出其他项目|元|证券、银行、保险|
|ttl\_cost\_oper\_other|float|营业总成本其他项目|元||
|***其他经营收益***|||元||
|inc\_inv|float|投资收益|元||
|inv\_inv\_jv\_p|float|对联营企业和合营企业的投资收益|元||
|inc\_ast\_dspl|float|资产处置收益|元||
|ast\_impr\_loss|float|资产减值损失(新)|元||
|cred\_impr\_loss|float|信用减值损失(新)|元||
|inc\_fv\_chg|float|公允价值变动收益|元||
|inc\_other|float|其他收益|元||
|oper\_prof\_balance|float|营业利润平衡项目|元||
|oper\_prof|float|营业利润|元||
|inc\_noper|float|营业外收入|元||
|exp\_noper|float|营业外支出|元||
|ttl\_prof\_balance|float|利润总额平衡项目|元||
|oper\_prof\_other|float|营业利润其他项目|元||
|ttl\_prof|float|利润总额|元||
|inc\_tax|float|所得税费用|元||
|net\_prof|float|净利润|元||
|oper\_net\_prof|float|持续经营净利润|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|min\_int\_inc|float|少数股东损益|元||
|end\_net\_prof|float|终止经营净利润|元||
|net\_prof\_other|float|净利润其他项目|元||
|eps\_base|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|other\_comp\_inc|float|其他综合收益|元||
|other\_comp\_inc\_pcom|float|归属于母公司股东的其他综合收益|元||
|other\_comp\_inc\_min|float|归属于少数股东的其他综合收益|元||
|ttl\_comp\_inc|float|综合收益总额|元||
|ttl\_comp\_inc\_pcom|float|归属于母公司所有者的综合收益总额|元||
|ttl\_comp\_inc\_min|float|归属于少数股东的综合收益总额|元||
|prof\_pre\_merge|float|被合并方在合并前实现利润|元||
|net\_rsv\_in\_contr|float|提取保险合同准备金净额|元||
|net\_pay\_comp|float|赔付支出净额|元||
|net\_loss\_ncur\_ast|float|非流动资产处置净损失|元||
|amod\_fin\_asst\_end|float|以摊余成本计量的金融资产终止确认收益|元||
|cash\_flow\_hedging\_pl|float|现金流量套期损益的有效部分|元||
|cur\_trans\_diff|float|外币财务报表折算差额|元||
|gain\_ncur\_ast|float|非流动资产处置利得|元||
|afs\_fv\_chg\_pl|float|可供出售金融资产公允价值变动损益|元||
|oth\_eqy\_inv\_fv\_chg|float|其他权益工具投资公允价值变动|元||
|oth\_debt\_inv\_fv\_chg|float|其他债权投资公允价值变动|元||
|oth\_debt\_inv\_cred\_impr|float|其他债权投资信用减值准备|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-prime-pt-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E4%B8%BB%E8%A6%81%E6%8C%87%E6%A0%87%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_finance_prime_pt`​ - 查询财务主要指标截面数据（多标的）

查询指定日期截面上，股票所属上市公司的财务主要指标数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_prime_pt(symbols, fields, rpt_type=None, data_type=None, date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------| -----------| --------| ----| ------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务主要指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[财务主要指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#main_financial)**|

**示例：**

```python
stk_get_finance_prime_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='eps_basic,eps_dil', rpt_type=None, data_type=None, date='2023-06-19', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  eps_dil  eps_basic
0  SZSE.000001  2023-04-25  2023-03-31         1        101   0.6500     0.6500
1  SZSE.300002  2023-04-27  2023-03-31         1        101   0.0914     0.0914
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期 `date` ​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**财务主要指标**

|字段名|类型|中文名称|量纲|说明|
| ------------------| -----| ------------------------------------------| ----| ----|
|eps\_basic|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|eps\_basic\_cut|float|扣除非经常性损益后的基本每股收益|元||
|eps\_dil\_cut|float|扣除非经常性损益后的稀释每股收益|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|bps\_pcom\_ps|float|归属于母公司股东的每股净资产|元||
|ttl\_ast|float|总资产|元||
|ttl\_liab|float|总负债|元||
|share\_cptl|float|股本|股||
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|oper\_prof|float|营业利润|元||
|ttl\_prof|float|利润总额|元||
|ttl\_eqy\_pcom|float|归属于母公司股东的所有者权益|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|net\_prof\_pcom\_cut|float|扣除非经常性损益后归属于母公司股东的净利润|元||
|roe|float|全面摊薄净资产收益率|%||
|roe\_weight\_avg|float|加权平均净资产收益率|%||
|roe\_cut|float|扣除非经常性损益后的全面摊薄净资产收益率|%||
|roe\_weight\_avg\_cut|float|扣除非经常性损益后的加权平均净资产收益率|%||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|eps\_yoy|float|每股收益同比比例|%||
|inc\_oper\_yoy|float|营业收入同比比例|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比比例|%||
|net\_prof\_pcom\_yoy|float|归母净利润同比比例|%||
|bps\_sh|float|归属于普通股东的每股净资产|元||
|net\_asset|float|归属于普通股东的净资产|元||
|net\_prof|float|归属于普通股东的净利润|元||
|net\_prof\_cut|float|扣除非经常性损益后归属于普通股股东的净利润|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-deriv-pt-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E8%A1%8D%E7%94%9F%E6%8C%87%E6%A0%87%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_finance_deriv_pt`​ - 查询财务衍生指标截面数据（多标的）

查询指定日期截面上，股票所属上市公司的财务衍生指标数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_deriv_pt(symbols, fields, rpt_type=None, data_type=None, date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------| -----------| --------| ----| ------| ---------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。 101-合并原始<br />102-合并调整 201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[财务衍生指标指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#financial_derivative)**|

**示例：**

```python
stk_get_finance_deriv_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='eps_basic,eps_dil2',
                                   rpt_type=None, data_type=None, date='2023-06-19', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  ...  data_type  eps_basic  eps_dil2
0  SZSE.000001  2023-04-25  2023-03-31  ...        102     0.6500    0.6500
1  SZSE.300002  2023-04-27  2023-03-31  ...        102     0.0914    0.0914
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期 `date` ​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**财务衍生指标指标**

|字段名|类型|中文名称|量纲|说明|
| --------------------------| -----| --------------------------------------------------| ----| ----|
|eps\_basic|float|每股收益 EPS(基本)|元||
|eps\_dil2|float|每股收益 EPS(稀释)|元||
|eps\_dil|float|每股收益 EPS(期末股本摊薄)|元||
|eps\_basic\_cut|float|每股收益 EPS(扣除/基本)|元||
|eps\_dil2\_cut|float|每股收益 EPS(扣除/稀释)|元||
|eps\_dil\_cut|float|每股收益 EPS(扣除/期末股本摊薄)|元||
|bps|float|每股净资产 BPS|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|ttl\_inc\_oper\_ps|float|每股营业总收入|元||
|inc\_oper\_ps|float|每股营业收入|元||
|ebit\_ps|float|每股息税前利润|元||
|cptl\_rsv\_ps|float|每股资本公积|元||
|sur\_rsv\_ps|float|每股盈余公积|元||
|retain\_prof\_ps|float|每股未分配利润|元||
|retain\_inc\_ps|float|每股留存收益|元||
|net\_cf\_ps|float|每股现金流量净额|元||
|fcff\_ps|float|每股企业自由现金流量|元||
|fcfe\_ps|float|每股股东自由现金流量|元||
|ebitda\_ps|float|每股 EBITDA|元||
|roe|float|净资产收益率 ROE(摊薄)|%||
|roe\_weight|float|净资产收益率 ROE(加权)|%||
|roe\_avg|float|净资产收益率 ROE(平均)|%||
|roe\_cut|float|净资产收益率 ROE(扣除/摊薄)|%||
|roe\_weight\_cut|float|净资产收益率 ROE(扣除/加权)|%||
|ocf\_toi|float|经营性现金净流量/营业总收入|||
|eps\_dil\_yoy|float|稀释每股收益同比增长率|%||
|net\_cf\_oper\_ps\_yoy|float|每股经营活动中产生的现金流量净额同比增长率|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比增长率|%||
|inc\_oper\_yoy|float|营业收入同比增长率|%||
|oper\_prof\_yoy|float|营业利润同比增长率|%||
|ttl\_prof\_yoy|float|利润总额同比增长率|%||
|net\_prof\_pcom\_yoy|float|归属母公司股东的净利润同比增长率|%||
|net\_prof\_pcom\_cut\_yoy|float|归属母公司股东的净利润同比增长率(扣除非经常性损益)|%||
|net\_cf\_oper\_yoy|float|经营活动产生的现金流量净额同比增长率|%||
|roe\_yoy|float|净资产收益率同比增长率(摊薄)|%||
|net\_asset\_yoy|float|净资产同比增长率|%||
|ttl\_liab\_yoy|float|总负债同比增长率|%||
|ttl\_asset\_yoy|float|总资产同比增长率|%||
|net\_cash\_flow\_yoy|float|现金净流量同比增长率|%||
|bps\_gr\_begin\_year|float|每股净资产相对年初增长率|%||
|ttl\_asset\_gr\_begin\_year|float|资产总计相对年初增长率|%||
|ttl\_eqy\_pcom\_gr\_begin\_year|float|归属母公司的股东权益相对年初增长率|%||
|net\_debt\_eqy\_ev|float|净债务/股权价值|%||
|int\_debt\_eqy\_ev|float|带息债务/股权价值|||
|eps\_bas\_yoy|float|基本每股收益同比增长率|%||
|ebit|float|EBIT(正推法)|元||
|ebitda|float|EBITDA(正推法)|元||
|ebit\_inverse|float|EBIT(反推法)|元||
|ebitda\_inverse|float|EBITDA(反推法)|元||
|nr\_prof\_loss|float|非经常性损益|元||
|net\_prof\_cut|float|扣除非经常性损益后的净利润|元||
|gross\_prof|float|毛利润|元||
|oper\_net\_inc|float|经营活动净收益|元||
|val\_chg\_net\_inc|float|价值变动净收益|元||
|exp\_rd|float|研发费用|元||
|ttl\_inv\_cptl|float|全部投入资本|元||
|work\_cptl|float|营运资本|元||
|net\_work\_cptl|float|净营运资本|元||
|tg\_asset|float|有形资产|元||
|retain\_inc|float|留存收益|元||
|int\_debt|float|带息债务|元||
|net\_debt|float|净债务|元||
|curr\_liab\_non\_int|float|无息流动负债|元||
|ncur\_liab\_non\_int|float|无息非流动负债|元||
|fcff|float|企业自由现金流量 FCFF|元||
|fcfe|float|股权自由现金流量 FCFE|元||
|cur\_depr\_amort|float|当期计提折旧与摊销|元||
|eqy\_mult\_dupont|float|权益乘数(杜邦分析)|||
|net\_prof\_pcom\_np|float|归属母公司股东的净利润/净利润|%||
|net\_prof\_tp|float|净利润/利润总额|%||
|ttl\_prof\_ebit|float|利润总额/息税前利润|%||
|roe\_cut\_avg|float|净资产收益率 ROE(扣除/平均)|%||
|roe\_add|float|净资产收益率 ROE(增发条件)|%||
|roe\_ann|float|净资产收益率 ROE(年化)|%||
|roa|float|总资产报酬率 ROA|%||
|roa\_ann|float|总资产报酬率 ROA(年化)|%||
|jroa|float|总资产净利率|%||
|jroa\_ann|float|总资产净利率(年化)|%||
|roic|float|投入资本回报率 ROIC|%||
|sale\_npm|float|销售净利率|%||
|sale\_gpm|float|销售毛利率|%||
|sale\_cost\_rate|float|销售成本率|%||
|sale\_exp\_rate|float|销售期间费用率|%||
|net\_prof\_toi|float|净利润/营业总收入|%||
|oper\_prof\_toi|float|营业利润/营业总收入|%||
|ebit\_toi|float|息税前利润/营业总收入|%||
|ttl\_cost\_oper\_toi|float|营业总成本/营业总收入|%||
|exp\_oper\_toi|float|营业费用/营业总收入|%||
|exp\_admin\_toi|float|管理费用/营业总收入|%||
|exp\_fin\_toi|float|财务费用/营业总收入|%||
|ast\_impr\_loss\_toi|float|资产减值损失/营业总收入|%||
|ebitda\_toi|float|EBITDA/营业总收入|%||
|oper\_net\_inc\_tp|float|经营活动净收益/利润总额|%||
|val\_chg\_net\_inc\_tp|float|价值变动净收益/利润总额|%||
|net\_exp\_noper\_tp|float|营业外支出净额/利润总额|||
|inc\_tax\_tp|float|所得税/利润总额|%||
|net\_prof\_cut\_np|float|扣除非经常性损益的净利润/净利润|%||
|eqy\_mult|float|权益乘数|||
|curr\_ast\_ta|float|流动资产/总资产|%||
|ncurr\_ast\_ta|float|非流动资产/总资产|%||
|tg\_ast\_ta|float|有形资产/总资产|%||
|ttl\_eqy\_pcom\_tic|float|归属母公司股东的权益/全部投入资本|%||
|int\_debt\_tic|float|带息负债/全部投入资本|%||
|curr\_liab\_tl|float|流动负债/负债合计|%||
|ncurr\_liab\_tl|float|非流动负债/负债合计|%||
|ast\_liab\_rate|float|资产负债率|%||
|quick\_rate|float|速动比率|||
|curr\_rate|float|流动比率|||
|cons\_quick\_rate|float|保守速动比率|||
|liab\_eqy\_rate|float|产权比率|||
|ttl\_eqy\_pcom\_tl|float|归属母公司股东的权益/负债合计|||
|ttl\_eqy\_pcom\_debt|float|归属母公司股东的权益/带息债务|||
|tg\_ast\_tl|float|有形资产/负债合计|||
|tg\_ast\_int\_debt|float|有形资产/带息债务|||
|tg\_ast\_net\_debt|float|有形资产/净债务|||
|ebitda\_tl|float|息税折旧摊销前利润/负债合计|||
|net\_cf\_oper\_tl|float|经营活动产生的现金流量净额/负债合计|||
|net\_cf\_oper\_int\_debt|float|经营活动产生的现金流量净额/带息债务|||
|net\_cf\_oper\_curr\_liab|float|经营活动产生的现金流量净额/流动负债|||
|net\_cf\_oper\_net\_liab|float|经营活动产生的现金流量净额/净债务|||
|ebit\_int\_cover|float|已获利息倍数|||
|long\_liab\_work\_cptl|float|长期债务与营运资金比率|||
|ebitda\_int\_debt|float|EBITDA/带息债务|%||
|oper\_cycle|float|营业周期|天||
|inv\_turnover\_days|float|存货周转天数|天||
|acct\_rcv\_turnover\_days|float|应收账款周转天数(含应收票据)|天||
|inv\_turnover\_rate|float|存货周转率|次||
|acct\_rcv\_turnover\_rate|float|应收账款周转率(含应收票据)|次||
|curr\_ast\_turnover\_rate|float|流动资产周转率|次||
|fix\_ast\_turnover\_rate|float|固定资产周转率|次||
|ttl\_ast\_turnover\_rate|float|总资产周转率|次||
|cash\_rcv\_sale\_oi|float|销售商品提供劳务收到的现金/营业收入|%||
|net\_cf\_oper\_oi|float|经营活动产生的现金流量净额/营业收入|%||
|net\_cf\_oper\_oni|float|经营活动产生的现金流量净额/经营活动净收益|||
|cptl\_exp\_da|float|资本支出/折旧摊销|%||
|cash\_rate|float|现金比率|||
|acct\_pay\_turnover\_days|float|应付账款周转天数(含应付票据)|天||
|acct\_pay\_turnover\_rate|float|应付账款周转率(含应付票据)|次||
|net\_oper\_cycle|float|净营业周期|天||
|ttl\_cost\_oper\_yoy|float|营业总成本同比增长率|%||
|net\_prof\_yoy|float|净利润同比增长率|%||
|net\_cf\_oper\_np|float|经营活动产生的现金流量净额/净利润|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-valuation-pt-%E6%9F%A5%E8%AF%A2%E4%BC%B0%E5%80%BC%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_valuation_pt`​ - 查询估值指标单日截面数据（多标的）

查询指定日期截面上，股票的单日估值指标（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_valuation_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| -----------| --------| ----| ------| ------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[估值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#valuation)**|

**示例：**

```python
stk_get_daily_valuation_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='pe_ttm,pe_lyr,pe_mrq',
                               trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date   pe_ttm   pe_mrq   pe_lyr
0  SZSE.000001  2023-06-26   4.5900   3.7145   4.7666
1  SZSE.300002  2023-06-26  39.3144  36.2480  47.6621
 
        复制成功
  
```

**注意：**

**1.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**估值指标**

|字段名|类型|中文名称|量纲|说明|
| ------------| -----| -------------------------------------------| ----| ----|
|pe\_ttm|float|市盈率(TTM)|倍||
|pe\_lyr|float|市盈率(最新年报 LYR)|倍||
|pe\_mrq|float|市盈率(最新报告期 MRQ)|倍||
|pe\_1q|float|市盈率(当年一季 ×4)|倍||
|pe\_2q|float|市盈率(当年中报 ×2)|倍||
|pe\_3q|float|市盈率(当年三季 ×4/3)|倍||
|pe\_ttm\_cut|float|市盈率(TTM) 扣除非经常性损益|倍||
|pe\_lyr\_cut|float|市盈率(最新年报 LYR) 扣除非经常性损益|倍||
|pe\_mrq\_cut|float|市盈率(最新报告期 MRQ) 扣除非经常性损益|倍||
|pe\_1q\_cut|float|市盈率(当年一季 ×4) 扣除非经常性损益|倍||
|pe\_2q\_cut|float|市盈率(当年中报 ×2) 扣除非经常性损益|倍||
|pe\_3q\_cut|float|市盈率(当年三季 ×4/3) 扣除非经常性损益|倍||
|pb\_lyr|float|市净率(最新年报 LYR)|倍||
|pb\_mrq|float|市净率(最新报告期 MRQ)|倍||
|pb\_lyr\_1|float|市净率(剔除其他权益工具，最新年报 LYR)|倍||
|pb\_mrq\_1|float|市净率(剔除其他权益工具，最新报告期 MRQ)|倍||
|pcf\_ttm\_oper|float|市现率(经营现金流,TTM)|倍||
|pcf\_ttm\_ncf|float|市现率(现金净流量,TTM)|倍||
|pcf\_lyr\_oper|float|市现率(经营现金流,最新年报 LYR)|倍||
|pcf\_lyr\_ncf|float|市现率(现金净流量,最新年报 LYR)|倍||
|ps\_ttm|float|市销率(TTM)|倍||
|ps\_lyr|float|市销率(最新年报 LYR)|倍||
|ps\_mrq|float|市销率(最新报告期 MRQ)|倍||
|ps\_1q|float|市销率(当年一季 ×4)|倍||
|ps\_2q|float|市销率(当年中报 ×2)|倍||
|ps\_3q|float|市销率(当年三季 ×4/3)|倍||
|peg\_lyr|float|历史 PEG 值(当年年报增长率)|||
|peg\_mrq|float|历史 PEG 值(最新报告期增长率)|||
|peg\_1q|float|历史 PEG 值(当年 1 季\*4 较上年年报增长率)|||
|peg\_2q|float|历史 PEG 值(当年中报\*2 较上年年报增长率)|||
|peg\_3q|float|历史 PEG 值(当年 3 季\*4/3 较上年年报增长率)|||
|peg\_np\_cgr|float|历史 PEG 值(PE\_TTM 较净利润 3 年复合增长率)|||
|peg\_npp\_cgr|float|历史 PEG 值(PE\_TTM 较净利润 3 年复合增长率)|||
|dy\_ttm|float|股息率(滚动 12 月 TTM)|%||
|dy\_lfy|float|股息率(上一财年 LFY)|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-mktvalue-pt-%E6%9F%A5%E8%AF%A2%E5%B8%82%E5%80%BC%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_mktvalue_pt`​ - 查询市值指标单日截面数据（多标的）

查询指定日期截面上，股票的单日市值截面数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_mktvalue_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| -----------| --------| ----| ------| ------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[市值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#mktvalue)**|

**示例：**

```python
stk_get_daily_mktvalue_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='tot_mv,tot_mv_csrc,a_mv',
                               trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date        a_mv      tot_mv  tot_mv_csrc
0  SZSE.000001  2023-06-26  2.1696e+11  2.1696e+11   2.1696e+11
1  SZSE.300002  2023-06-26  2.5828e+10  2.5828e+10   2.5828e+10
 
        复制成功
  
```

**注意：**

**1.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**市值指标**

|字段名|类型|中文名称|量纲|说明|
| ------------| -----| ----------------------------------| ----| ----|
|tot\_mv|float|总市值|元||
|tot\_mv\_csrc|float|总市值(证监会算法)|元||
|a\_mv|float|A 股流通市值(含限售股)|元||
|a\_mv\_ex\_ltd|float|A 股流通市值(不含限售股)|元||
|b\_mv|float|B 股流通市值(含限售股，折人民币)|元||
|b\_mv\_ex\_ltd|float|B 股流通市值(不含限售股，折人民币)|元||
|ev|float|企业价值(含货币资金)(EV1)|元||
|ev\_ex\_curr|float|企业价值(剔除货币资金)(EV2)|元||
|ev\_ebitda|float|企业倍数|倍||
|equity\_value|float|股权价值|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-basic-pt-%E6%9F%A5%E8%AF%A2%E8%82%A1%E6%9C%AC%E7%AD%89%E5%9F%BA%E7%A1%80%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_basic_pt`​ - 查询股本等基础指标单日截面数据（多标的）

查询指定日期截面上，股票的单日基础指标截面数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_basic_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| -----------| --------| ----| ------| ------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields` ​字段的数值. 支持的字段名请参考 **[基础指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#daily_basic)**|

**示例：**

```python
stk_get_daily_basic_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='tclose,turnrate,ttl_shr',
                                  trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  turnrate  tclose     ttl_shr
0  SZSE.000001  2023-06-27    0.2379   11.28  1.9406e+10
1  SZSE.300002  2023-06-27    7.3596   13.44  1.9611e+09
 
        复制成功
  
```

**注意：**

**1.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段 `""`​，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**基础指标**

|字段名|类型|中文名称|量纲|说明|
| -----------| -----| ------------------------------------------------------------| ----| ----|
|tclose|float|收盘价|元||
|turnrate|float|当日换手率|%||
|ttl\_shr|float|总股本|股||
|circ\_shr|float|流通股本（流通股本\=无限售条件流通股本 + 有限售条件流通股本）|股||
|ttl\_shr\_unl|float|无限售条件流通股本(行情软件定义的流通股)|股||
|ttl\_shr\_ltd|float|有限售条件股本|股||
|a\_shr\_unl|float|无限售条件流通股本(A 股)|股||
|h\_shr\_unl|float|无限售条件流通股本(H 股)|股||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-balance-%E6%9F%A5%E8%AF%A2%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_balance`​ - 查询资产负债表数据

查询指定时间段某一股票所属上市公司的资产负债表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_balance(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| -------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[资产负债表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#balance)**|

**示例：**

```python
stk_get_fundamentals_balance(symbol='SHSE.600000', rpt_type=12, data_type=None, start_date='2022-12-31', end_date='2022-12-31', fields='lt_eqy_inv', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type    lt_eqy_inv
0  SHSE.600000  2022-10-29  2021-12-31        12        102 2819000000.00
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近报告日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**资产负债表**

|字段名|类型|中文名称|量纲|说明|
| ---------------------| -----| ----------------------------------------------------------| ----| ----------------|
|***流动资产(资产)***|||||
|cash\_bal\_cb|float|现金及存放中央银行款项|元|银行|
|dpst\_ob|float|存放同业款项|元|银行|
|mny\_cptl|float|货币资金|元||
|cust\_cred\_dpst|float|客户信用资金存款|元|证券|
|cust\_dpst|float|客户资金存款|元|证券|
|pm|float|贵金属|元|银行|
|bal\_clr|float|结算备付金|元||
|cust\_rsv|float|客户备付金|元|证券|
|ln\_to\_ob|float|拆出资金|元||
|fair\_val\_fin\_ast|float|以公允价值计量且其变动计入当期损益的金融资产|元||
|ppay|float|预付款项|元||
|fin\_out|float|融出资金|元||
|trd\_fin\_ast|float|交易性金融资产|元||
|deriv\_fin\_ast|float|衍生金融资产|元||
|note\_acct\_rcv|float|应收票据及应收账款|元||
|note\_rcv|float|应收票据|元||
|acct\_rcv|float|应收账款|元||
|acct\_rcv\_fin|float|应收款项融资|元||
|int\_rcv|float|应收利息|元||
|dvd\_rcv|float|应收股利|元||
|oth\_rcv|float|其他应收款|元||
|in\_prem\_rcv|float|应收保费|元||
|rin\_acct\_rcv|float|应收分保账款|元||
|rin\_rsv\_rcv|float|应收分保合同准备金|元|保险|
|rcv\_un\_prem\_rin\_rsv|float|应收分保未到期责任准备金|元||
|rcv\_clm\_rin\_rsv|float|应收分保未决赔偿准备金|元|保险|
|rcv\_li\_rin\_rsv|float|应收分保寿险责任准备金|元|保险|
|rcv\_lt\_hi\_rin\_rsv|float|应收分保长期健康险责任准备金|元|保险|
|ph\_plge\_ln|float|保户质押贷款|元|保险|
|ttl\_oth\_rcv|float|其他应收款合计|元||
|rfd\_dpst|float|存出保证金|元|证券、保险|
|term\_dpst|float|定期存款|元|保险|
|pur\_resell\_fin|float|买入返售金融资产|元||
|aval\_sale\_fin|float|可供出售金融资产|元||
|htm\_inv|float|持有至到期投资|元||
|hold\_for\_sale|float|持有待售资产|元||
|acct\_rcv\_inv|float|应收款项类投资|元|保险|
|invt|float|存货|元||
|contr\_ast|float|合同资产|元||
|ncur\_ast\_one\_y|float|一年内到期的非流动资产|元||
|oth\_cur\_ast|float|其他流动资产|元||
|cur\_ast\_oth\_item|float|流动资产其他项目|元||
|ttl\_cur\_ast|float|流动资产合计|元||
|***非流动资产(资产)***|||||
|loan\_adv|float|发放委托贷款及垫款|元||
|cred\_inv|float|债权投资|元||
|oth\_cred\_inv|float|其他债权投资|元||
|lt\_rcv|float|长期应收款|元||
|lt\_eqy\_inv|float|长期股权投资|元||
|oth\_eqy\_inv|float|其他权益工具投资|元||
|rfd\_cap\_guar\_dpst|float|存出资本保证金|元|保险|
|oth\_ncur\_fin\_ast|float|其他非流动金融资产|元||
|amor\_cos\_fin\_ast\_ncur|float|以摊余成本计量的金融资产（非流动）|元||
|fair\_val\_oth\_inc\_ncur|float|以公允价值计量且其变动计入其他综合收益的金融资产（非流动）|元||
|inv\_prop|float|投资性房地产|元||
|fix\_ast|float|固定资产|元||
|const\_prog|float|在建工程|元||
|const\_matl|float|工程物资|元||
|fix\_ast\_dlpl|float|固定资产清理|元||
|cptl\_bio\_ast|float|生产性生物资产|元||
|oil\_gas\_ast|float|油气资产|元||
|rig\_ast|float|使用权资产|元||
|intg\_ast|float|无形资产|元||
|trd\_seat\_fee|float|交易席位费|元|证券|
|dev\_exp|float|开发支出|元||
|gw|float|商誉|元||
|lt\_ppay\_exp|float|长期待摊费用|元||
|dfr\_tax\_ast|float|递延所得税资产|元||
|oth\_ncur\_ast|float|其他非流动资产|元||
|ncur\_ast\_oth\_item|float|非流动资产其他项目|元||
|ttl\_ncur\_ast|float|非流动资产合计|元||
|oth\_ast|float|其他资产|元|银行、证券、保险|
|ast\_oth\_item|float|资产其他项目|元||
|ind\_acct\_ast|float|独立账户资产|元|保险|
|ttl\_ast|float|资产总计|元||
|***流动负债(负债)***|||||
|brw\_cb|float|向中央银行借款|元||
|dpst\_ob\_fin\_inst|float|同业和其他金融机构存放款项|元|银行、保险|
|ln\_fm\_ob|float|拆入资金|元||
|fair\_val\_fin\_liab|float|以公允价值计量且其变动计入当期损益的金融负债|元||
|sht\_ln|float|短期借款|元||
|adv\_acct|float|预收款项|元||
|contr\_liab|float|合同负债|元||
|trd\_fin\_liab|float|交易性金融负债|元||
|deriv\_fin\_liab|float|衍生金融负债|元||
|sell\_repo\_ast|float|卖出回购金融资产款|元||
|cust\_bnk\_dpst|float|吸收存款|元|银行、保险|
|dpst\_cb\_note\_pay|float|存款证及应付票据|元|银行|
|dpst\_cb|float|存款证|元|银行|
|acct\_rcv\_adv|float|预收账款|元|保险|
|in\_prem\_rcv\_adv|float|预收保费|元|保险|
|fee\_pay|float|应付手续费及佣金|元||
|note\_acct\_pay|float|应付票据及应付账款|元||
|stlf\_pay|float|应付短期融资款|元||
|note\_pay|float|应付票据|元||
|acct\_pay|float|应付账款|元||
|rin\_acct\_pay|float|应付分保账款|元||
|emp\_comp\_pay|float|应付职工薪酬|元||
|tax\_pay|float|应交税费|元||
|int\_pay|float|应付利息|元||
|dvd\_pay|float|应付股利|元||
|ph\_dvd\_pay|float|应付保单红利|元|保险|
|indem\_pay|float|应付赔付款|元|保险|
|oth\_pay|float|其他应付款|元||
|ttl\_oth\_pay|float|其他应付款合计|元||
|ph\_dpst\_inv|float|保户储金及投资款|元|保险|
|in\_contr\_rsv|float|保险合同准备金|元|保险|
|un\_prem\_rsv|float|未到期责任准备金|元|保险|
|clm\_rin\_rsv|float|未决赔款准备金|元|保险|
|li\_liab\_rsv|float|寿险责任准备金|元|保险|
|lt\_hi\_liab\_rsv|float|长期健康险责任准备金|元|保险|
|cust\_bnk\_dpst\_fin|float|吸收存款及同业存放|元||
|inter\_pay|float|内部应付款|元||
|agy\_secu\_trd|float|代理买卖证券款|元||
|agy\_secu\_uw|float|代理承销证券款|元||
|sht\_bnd\_pay|float|应付短期债券|元||
|est\_cur\_liab|float|预计流动负债|元||
|liab\_hold\_for\_sale|float|持有待售负债|元||
|ncur\_liab\_one\_y|float|一年内到期的非流动负债|元||
|oth\_cur\_liab|float|其他流动负债|元||
|cur\_liab\_oth\_item|float|流动负债其他项目|元||
|ttl\_cur\_liab|float|流动负债合计|元||
|***非流动负债（负债）***|||||
|lt\_ln|float|长期借款|元||
|lt\_pay|float|长期应付款|元||
|leas\_liab|float|租赁负债|||
|dfr\_inc|float|递延收益|元||
|dfr\_tax\_liab|float|递延所得税负债|元||
|bnd\_pay|float|应付债券|元||
|bnd\_pay\_pbd|float|其中:永续债|元||
|bnd\_pay\_pfd|float|其中:优先股|元||
|oth\_ncur\_liab|float|其他非流动负债|元||
|spcl\_pay|float|专项应付款|元||
|ncur\_liab\_oth\_item|float|非流动负债其他项目|元||
|lt\_emp\_comp\_pay|float|长期应付职工薪酬|元||
|est\_liab|float|预计负债|元||
|oth\_liab|float|其他负债|元|银行、证券、保险|
|liab\_oth\_item|float|负债其他项目|元|银行、证券、保险|
|ttl\_ncur\_liab|float|非流动负债合计|元||
|ind\_acct\_liab|float|独立账户负债|元|保险|
|ttl\_liab|float|负债合计|元||
|***所有者权益(或股东权益)***|||||
|paid\_in\_cptl|float|实收资本（或股本）|元||
|oth\_eqy|float|其他权益工具|元||
|oth\_eqy\_pfd|float|其中:优先股|元||
|oth\_eqy\_pbd|float|其中:永续债|元||
|oth\_eqy\_oth|float|其中:其他权益工具|元||
|cptl\_rsv|float|资本公积|元||
|treas\_shr|float|库存股|元||
|oth\_comp\_inc|float|其他综合收益|元||
|spcl\_rsv|float|专项储备|元||
|sur\_rsv|float|盈余公积|元||
|rsv\_ord\_rsk|float|一般风险准备|元||
|trd\_risk\_rsv|float|交易风险准备|元|证券|
|ret\_prof|float|未分配利润|元||
|sugg\_dvd|float|建议分派股利|元|银行|
|eqy\_pcom\_oth\_item|float|归属于母公司股东权益其他项目|元||
|ttl\_eqy\_pcom|float|归属于母公司股东权益合计|元||
|min\_sheqy|float|少数股东权益|元||
|sheqy\_oth\_item|float|股东权益其他项目|元||
|ttl\_eqy|float|股东权益合计|元||
|ttl\_liab\_eqy|float|负债和股东权益合计|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-cashflow-%E6%9F%A5%E8%AF%A2%E7%8E%B0%E9%87%91%E6%B5%81%E9%87%8F%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_cashflow`​ - 查询现金流量表数据

查询指定时间段某一股票所属上市公司的现金流量表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_cashflow(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| -------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[现金流量表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#cashflow)**|

**示例：**

```python
stk_get_fundamentals_cashflow(symbol='SHSE.600000', rpt_type=None, data_type=101, start_date='2022-12-31', end_date='2022-12-31', fields='cash_pay_fee', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  cash_pay_fee
0  SHSE.600000  2022-10-29  2022-09-30         9        101 7261000000.00
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近报告日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**现金流量表**

|字段名|类型|中文名称|量纲|说明|
| ------------------------| -----| ---------------------------------------------------| ----| ----------------|
|***一、经营活动产生的现金流量***|||||
|cash\_rcv\_sale|float|销售商品、提供劳务收到的现金|元||
|net\_incr\_cust\_dpst\_ob|float|客户存款和同业存放款项净增加额|元||
|net\_incr\_cust\_dpst|float|客户存款净增加额|元|银行|
|net\_incr\_dpst\_ob|float|同业及其他金融机构存放款项净增加额|元|银行|
|net\_incr\_brw\_cb|float|向中央银行借款净增加额|元||
|net\_incr\_ln\_fm\_oth|float|向其他金融机构拆入资金净增加额|元||
|cash\_rcv\_orig\_in|float|收到原保险合同保费取得的现金|元||
|net\_cash\_rcv\_rin\_biz|float|收到再保险业务现金净额|元||
|net\_incr\_ph\_dpst\_inv|float|保户储金及投资款净增加额|元||
|net\_decrdpst\_cb\_ob|float|存放中央银行和同业款项及其他金融机构净减少额|元|银行、保险|
|net\_decr\_cb|float|存放中央银行款项净减少额|元|银行|
|net\_decr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净减少额|元|银行|
|net\_cert\_dpst|float|存款证净额|元|银行|
|net\_decr\_trd\_fin|float|交易性金融资产净减少额|元|银行|
|net\_incr\_trd\_liab|float|交易性金融负债净增加额|元|银行|
|cash\_rcv\_int\_fee|float|收取利息、手续费及佣金的现金|元||
|cash\_rcv\_int|float|其中：收取利息的现金|元|银行|
|cash\_rcv\_fee|float|收取手续费及佣金的现金|元|银行|
|net\_incr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净增加额|元|银行|
|net\_incr\_ln\_fm|float|拆入资金净增加额|元||
|net\_incr\_sell\_repo|float|卖出回购金融资产款净增加额|元|银行 保险|
|net\_decr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净减少额|元|银行|
|net\_decr\_ln\_cptl|float|拆出资金净减少额|元|银行、保险|
|net\_dect\_pur\_resell|float|买入返售金融资产净减少额|元|银行、保险|
|net\_incr\_repo|float|回购业务资金净增加额|元||
|net\_decr\_repo|float|回购业务资金净减少额|元|证券|
|tax\_rbt\_rcv|float|收到的税费返还|元||
|net\_cash\_rcv\_trd|float|收到交易性金融资产现金净额|元|保险|
|cash\_rcv\_oth\_oper|float|收到其他与经营活动有关的现金|元||
|net\_cash\_agy\_secu\_trd|float|代理买卖证券收到的现金净额|元|证券|
|cash\_rcv\_pur\_resell|float|买入返售金融资产收到的现金|元|证券、保险|
|net\_cash\_agy\_secu\_uw|float|代理承销证券收到的现金净额|元|证券|
|cash\_rcv\_dspl\_debt|float|处置抵债资产收到的现金|元|银行|
|canc\_loan\_rcv|float|收回的已于以前年度核销的贷款|元|银行|
|cf\_in\_oper|float|经营活动现金流入小计|元||
|cash\_pur\_gds\_svc|float|购买商品、接受劳务支付的现金|元||
|net\_incr\_ln\_adv\_cust|float|客户贷款及垫款净增加额|元||
|net\_decr\_brw\_cb|float|向中央银行借款净减少额|元|银行|
|net\_incr\_dpst\_cb\_ob|float|存放中央银行和同业款项净增加额|元||
|net\_incr\_cb|float|存放中央银行款项净增加额|元|银行|
|net\_incr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净增加额|元|银行|
|net\_decr\_dpst\_ob|float|同业及其他机构存放款减少净额|元|银行|
|net\_decr\_issu\_cert\_dpst|float|已发行存款证净减少额|元|银行|
|net\_incr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净增加额|元|银行|
|net\_incr\_ln\_to|float|拆出资金净增加额|元|银行、保险|
|net\_incr\_pur\_resell|float|买入返售金融资产净增加额|元|银行、保险|
|net\_decr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净减少额|元|银行|
|net\_decr\_ln\_fm|float|拆入资金净减少额|元|银行、证券|
|net\_decr\_sell\_repo|float|卖出回购金融资产净减少额|元|银行、保险|
|net\_incr\_trd\_fin|float|交易性金融资产净增加额|元|银行|
|net\_decr\_trd\_liab|float|交易性金融负债净减少额|元|银行|
|cash\_pay\_indem\_orig|float|支付原保险合同赔付款项的现金|元||
|net\_cash\_pay\_rin\_biz|float|支付再保险业务现金净额|元|保险|
|cash\_pay\_int\_fee|float|支付利息、手续费及佣金的现金|元||
|cash\_pay\_int|float|其中：支付利息的现金|元|银行|
|cash\_pay\_fee|float|支付手续费及佣金的现金|元|银行|
|ph\_dvd\_pay|float|支付保单红利的现金|元||
|net\_decr\_ph\_dpst\_inv|float|保户储金及投资款净减少额|元|保险|
|cash\_pay\_emp|float|支付给职工以及为职工支付的现金|||
|cash\_pay\_tax|float|支付的各项税费|元||
|net\_cash\_pay\_trd|float|支付交易性金融资产现金净额|元|保险|
|cash\_pay\_oth\_oper|float|支付其他与经营活动有关的现金|元||
|net\_incr\_dspl\_trd\_fin|float|处置交易性金融资产净增加额|元||
|cash\_pay\_fin\_leas|float|购买融资租赁资产支付的现金|元|银行|
|net\_decr\_agy\_secu\_pay|float|代理买卖证券支付的现金净额（净减少额）|元|证券|
|net\_decr\_dspl\_trd\_fin|float|处置交易性金融资产的净减少额|元|证券|
|cf\_out\_oper|float|经营活动现金流出小计|元||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|***二、投资活动产生的现金流量：***|||||
|cash\_rcv\_sale\_inv|float|收回投资收到的现金|元||
|inv\_inc\_rcv|float|取得投资收益收到的现金|元||
|cash\_rcv\_dvd\_prof|float|分得股利或利润所收到的现金|元|银行|
|cash\_rcv\_dspl\_ast|float|处置固定资产、无形资产和其他长期资产收回的现金净额|元||
|cash\_rcv\_dspl\_sub\_oth|float|处置子公司及其他营业单位收到的现金净额|元||
|cash\_rcv\_oth\_inv|float|收到其他与投资活动有关的现金|元||
|cf\_in\_inv|float|投资活动现金流入小计|元||
|pur\_fix\_intg\_ast|float|购建固定资产、无形资产和其他长期资产支付的现金|元||
|cash\_out\_dspl\_sub\_oth|float|处置子公司及其他营业单位流出的现金净额|元|保险|
|cash\_pay\_inv|float|投资支付的现金|元||
|net\_incr\_ph\_plge\_ln|float|保户质押贷款净增加额|元|保险|
|add\_cash\_pled\_dpst|float|增加质押和定期存款所支付的现金|元||
|net\_incr\_plge\_ln|float|质押贷款净增加额|元||
|net\_cash\_get\_sub|float|取得子公司及其他营业单位支付的现金净额|元||
|net\_pay\_pur\_resell|float|支付买入返售金融资产现金净额|元|证券、保险|
|cash\_pay\_oth\_inv|float|支付其他与投资活动有关的现金|元||
|cf\_out\_inv|float|投资活动现金流出小计|||
|net\_cf\_inv|float|投资活动产生的现金流量净额|元||
|***三、筹资活动产生的现金流量：***|||||
|cash\_rcv\_cptl|float|吸收投资收到的现金|元||
|sub\_rcv\_ms\_inv|float|其中：子公司吸收少数股东投资收到的现金|元||
|brw\_rcv|float|取得借款收到的现金|元||
|cash\_rcv\_bnd\_iss|float|发行债券收到的现金|元||
|net\_cash\_rcv\_sell\_repo|float|收到卖出回购金融资产款现金净额|元|保险|
|cash\_rcv\_oth\_fin|float|收到其他与筹资活动有关的现金|元||
|issu\_cert\_dpst|float|发行存款证|元|银行|
|cf\_in\_fin\_oth|float|筹资活动现金流入其他项目|元||
|cf\_in\_fin|float|筹资活动现金流入小计|元||
|cash\_rpay\_brw|float|偿还债务支付的现金|元||
|cash\_pay\_bnd\_int|float|偿付债券利息支付的现金|元|银行|
|cash\_pay\_dvd\_int|float|分配股利、利润或偿付利息支付的现金|元||
|sub\_pay\_dvd\_prof|float|其中：子公司支付给少数股东的股利、利润|元||
|cash\_pay\_oth\_fin|float|支付其他与筹资活动有关的现金|元||
|net\_cash\_pay\_sell\_repo|float|支付卖出回购金融资产款现金净额|元|保险|
|cf\_out\_fin|float|筹资活动现金流出小计|元||
|net\_cf\_fin|float|筹资活动产生的现金流量净额|元||
|efct\_er\_chg\_cash|float|四、汇率变动对现金及现金等价物的影响|元||
|net\_incr\_cash\_eq|float|五、现金及现金等价物净增加额|元||
|cash\_cash\_eq\_bgn|float|加：期初现金及现金等价物余额|元||
|cash\_cash\_eq\_end|float|六、期末现金及现金等价物余额|元||
|***补充资料 1．将净利润调节为经营活动现金流量：***|||||
|net\_prof|float|净利润|元||
|ast\_impr|float|资产减值准备|元||
|accr\_prvs\_ln\_impa|float|计提贷款减值准备|元|银行|
|accr\_prvs\_oth\_impa|float|计提其他资产减值准备|元|银行|
|accr\_prem\_rsv|float|提取的保险责任准备金|元|保险|
|accr\_unearn\_prem\_rsv|float|提取的未到期的责任准备金|元|保险|
|defr\_fix\_prop|float|固定资产和投资性房地产折旧|元||
|depr\_oga\_cba|float|其中:固定资产折旧、油气资产折耗、生产性生物资产折旧|元||
|amor\_intg\_ast\_lt\_exp|float|无形资产及长期待摊费用等摊销|元|银行、证券、保险|
|amort\_intg\_ast|float|无形资产摊销|元||
|amort\_lt\_exp\_ppay|float|长期待摊费用摊销|元||
|dspl\_ast\_loss|float|处置固定资产、无形资产和其他长期资产的损失|元||
|fair\_val\_chg\_loss|float|固定资产报废损失|元||
|fv\_chg\_loss|float|公允价值变动损失|元||
|dfa|float|固定资产折旧|元|银行|
|fin\_exp|float|财务费用|元||
|inv\_loss|float|投资损失|元||
|exchg\_loss|float|汇兑损失|元|银行、证券、保险|
|dest\_incr|float|存款的增加|元|银行|
|loan\_decr|float|贷款的减少|元|银行|
|cash\_pay\_bnd\_int\_iss|float|发行债券利息支出|元|银行|
|dfr\_tax|float|递延所得税|元||
|dfr\_tax\_ast\_decr|float|其中:递延所得税资产减少|元||
|dfr\_tax\_liab\_incr|float|递延所得税负债增加|元||
|invt\_decr|float|存货的减少|元||
|decr\_rcv\_oper|float|经营性应收项目的减少|元||
|incr\_pay\_oper|float|经营性应付项目的增加|元||
|oth|float|其他|元||
|cash\_end|float|现金的期末余额|元||
|cash\_bgn|float|减：现金的期初余额|元||
|cash\_eq\_end|float|加:现金等价物的期末余额|元||
|cash\_eq\_bgn|float|减:现金等价物的期初余额|元||
|cred\_impr\_loss|float|信用减值损失|元||
|est\_liab\_add|float|预计负债的增加|元||
|dr\_cnv\_cptl|float|债务转为资本|元||
|cptl\_bnd\_expr\_one\_y|float|一年内到期的可转换公司债券|元||
|fin\_ls\_fix\_ast|float|融资租入固定资产|元||
|amort\_dfr\_inc|float|递延收益摊销|元||
|depr\_inv\_prop|float|投资性房地产折旧|元||
|trd\_fin\_decr|float|交易性金融资产的减少|元|证券、保险|
|im\_net\_cf\_oper|float|间接法-经营活动产生的现金流量净额|元||
|im\_net\_incr\_cash\_eq|float|间接法-现金及现金等价物净增加额|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-income-%E6%9F%A5%E8%AF%A2%E5%88%A9%E6%B6%A6%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_income`​ - 查询利润表数据

查询指定时间段某一股票所属上市公司的利润表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_income(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| -------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[利润表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#income)**|

**示例：**

```python
stk_get_fundamentals_income(symbol='SHSE.600000', rpt_type=6, data_type=None, start_date='2022-12-31', end_date='2022-12-31', fields='inc_oper', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type       inc_oper
0  SHSE.600000  2022-08-27  2022-06-30         6        102 98644000000.00
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近报告日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**利润表**

|字段名|类型|中文名称|量纲|说明|
| ----------------------| -----| ------------------------------------| ----| ----------------|
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|net\_inc\_int|float|利息净收入|元|证券、银行、保险|
|exp\_int|float|利息支出|元||
|net\_inc\_fee\_comm|float|手续费及佣金净收入|元|证券、银行|
|inc\_rin\_prem|float|其中：分保费收入|元|保险|
|net\_inc\_secu\_agy|float|其中:代理买卖证券业务净收入|元|证券|
|inc\_fee\_comm|float|手续费及佣金收入|元||
|in\_prem\_earn|float|已赚保费|元|保险|
|inc\_in\_biz|float|其中:保险业务收入|元|保险|
|rin\_prem\_cede|float|分出保费|元|保险|
|unear\_prem\_rsv|float|提取未到期责任准备金|元|保险|
|net\_inc\_uw|float|证券承销业务净收入|元|证券|
|net\_inc\_cust\_ast\_mgmt|float|受托客户资产管理业务净收入|元|证券|
|inc\_fx|float|汇兑收益|元||
|inc\_other\_oper|float|其他业务收入|元||
|inc\_oper\_balance|float|营业收入平衡项目|元||
|ttl\_inc\_oper\_other|float|营业总收入其他项目|元||
|ttl\_cost\_oper|float|营业总成本|元||
|cost\_oper|float|营业成本|元||
|exp\_oper|float|营业支出|元|证券、银行、保险|
|biz\_tax\_sur|float|营业税金及附加|元||
|exp\_sell|float|销售费用|元||
|exp\_adm|float|管理费用|元||
|exp\_rd|float|研发费用|元||
|exp\_fin|float|财务费用|元||
|int\_fee|float|其中:利息费用|元||
|inc\_int|float|利息收入|元||
|exp\_oper\_adm|float|业务及管理费|元|证券、银行、保险|
|exp\_rin|float|减:摊回分保费用|元|保险|
|rfd\_prem|float|退保金|元|保险|
|comp\_pay|float|赔付支出|元|保险|
|rin\_clm\_pay|float|减:摊回赔付支出|元|保险|
|draw\_insur\_liab|float|提取保险责任准备金|元|保险|
|amor\_insur\_liab|float|减:摊回保险责任准备金|元|保险|
|exp\_ph\_dvd|float|保单红利支出|元|保险|
|exp\_fee\_comm|float|手续费及佣金支出|元||
|other\_oper\_cost|float|其他业务成本|元||
|oper\_exp\_balance|float|营业支出平衡项目|元|证券、银行、保险|
|exp\_oper\_other|float|营业支出其他项目|元|证券、银行、保险|
|ttl\_cost\_oper\_other|float|营业总成本其他项目|元||
|***其他经营收益***|||元||
|inc\_inv|float|投资收益|元||
|inv\_inv\_jv\_p|float|对联营企业和合营企业的投资收益|元||
|inc\_ast\_dspl|float|资产处置收益|元||
|ast\_impr\_loss|float|资产减值损失(新)|元||
|cred\_impr\_loss|float|信用减值损失(新)|元||
|inc\_fv\_chg|float|公允价值变动收益|元||
|inc\_other|float|其他收益|元||
|oper\_prof\_balance|float|营业利润平衡项目|元||
|oper\_prof|float|营业利润|元||
|inc\_noper|float|营业外收入|元||
|exp\_noper|float|营业外支出|元||
|ttl\_prof\_balance|float|利润总额平衡项目|元||
|oper\_prof\_other|float|营业利润其他项目|元||
|ttl\_prof|float|利润总额|元||
|inc\_tax|float|所得税费用|元||
|net\_prof|float|净利润|元||
|oper\_net\_prof|float|持续经营净利润|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|min\_int\_inc|float|少数股东损益|元||
|end\_net\_prof|float|终止经营净利润|元||
|net\_prof\_other|float|净利润其他项目|元||
|eps\_base|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|other\_comp\_inc|float|其他综合收益|元||
|other\_comp\_inc\_pcom|float|归属于母公司股东的其他综合收益|元||
|other\_comp\_inc\_min|float|归属于少数股东的其他综合收益|元||
|ttl\_comp\_inc|float|综合收益总额|元||
|ttl\_comp\_inc\_pcom|float|归属于母公司所有者的综合收益总额|元||
|ttl\_comp\_inc\_min|float|归属于少数股东的综合收益总额|元||
|prof\_pre\_merge|float|被合并方在合并前实现利润|元||
|net\_rsv\_in\_contr|float|提取保险合同准备金净额|元||
|net\_pay\_comp|float|赔付支出净额|元||
|net\_loss\_ncur\_ast|float|非流动资产处置净损失|元||
|amod\_fin\_asst\_end|float|以摊余成本计量的金融资产终止确认收益|元||
|cash\_flow\_hedging\_pl|float|现金流量套期损益的有效部分|元||
|cur\_trans\_diff|float|外币财务报表折算差额|元||
|gain\_ncur\_ast|float|非流动资产处置利得|元||
|afs\_fv\_chg\_pl|float|可供出售金融资产公允价值变动损益|元||
|oth\_eqy\_inv\_fv\_chg|float|其他权益工具投资公允价值变动|元||
|oth\_debt\_inv\_fv\_chg|float|其他债权投资公允价值变动|元||
|oth\_debt\_inv\_cred\_impr|float|其他债权投资信用减值准备|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-prime-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E4%B8%BB%E8%A6%81%E6%8C%87%E6%A0%87%E6%95%B0%E6%8D%AE)​`stk_get_finance_prime`​ - 查询财务主要指标数据

查询指定时间段股票所属上市公司的财务主要指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_prime(symbol, fields, rpt_type=None, data_type=None, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| -------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务主要指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[财务主要指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#main_financial)**|

**示例：**

```python
stk_get_finance_prime(symbol='SHSE.600000', fields='eps_basic,eps_dil',rpt_type=None, data_type=None,
start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  eps_dil  eps_basic
0  SHSE.600000  2023-04-29  2023-03-31         1        101     0.47       0.51
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近报告日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**财务主要指标**

|字段名|类型|中文名称|量纲|说明|
| ------------------| -----| ------------------------------------------| ----| ----|
|eps\_basic|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|eps\_basic\_cut|float|扣除非经常性损益后的基本每股收益|元||
|eps\_dil\_cut|float|扣除非经常性损益后的稀释每股收益|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|bps\_pcom\_ps|float|归属于母公司股东的每股净资产|元||
|ttl\_ast|float|总资产|元||
|ttl\_liab|float|总负债|元||
|share\_cptl|float|股本|股||
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|oper\_prof|float|营业利润|元||
|ttl\_prof|float|利润总额|元||
|ttl\_eqy\_pcom|float|归属于母公司股东的所有者权益|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|net\_prof\_pcom\_cut|float|扣除非经常性损益后归属于母公司股东的净利润|元||
|roe|float|全面摊薄净资产收益率|%||
|roe\_weight\_avg|float|加权平均净资产收益率|%||
|roe\_cut|float|扣除非经常性损益后的全面摊薄净资产收益率|%||
|roe\_weight\_avg\_cut|float|扣除非经常性损益后的加权平均净资产收益率|%||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|eps\_yoy|float|每股收益同比比例|%||
|inc\_oper\_yoy|float|营业收入同比比例|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比比例|%||
|net\_prof\_pcom\_yoy|float|归母净利润同比比例|%||
|bps\_sh|float|归属于普通股东的每股净资产|元||
|net\_asset|float|归属于普通股东的净资产|元||
|net\_prof|float|归属于普通股东的净利润|元||
|net\_prof\_cut|float|扣除非经常性损益后归属于普通股股东的净利润|元||

python 股票与指数数据 API 包含在 gm3.0.148 版本及以上版本

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-index-constituents-%E6%9F%A5%E8%AF%A2%E6%8C%87%E6%95%B0%E6%88%90%E5%88%86%E8%82%A1)​`stk_get_index_constituents`​ - 查询指数成分股

查询指定指数在最新交易日的成分股和权重(中证系列指数，因版权不提供成分股权重，weight\=0)

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_index_constituents(index, trade_date=None)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ----------------------------------------------|
|index|str|指数代码|Y|无|必填，只能输入一个指数，如：`'SHSE.000905'`​|
|trade\_date|str|交易日期|N|None|交易日期，%Y-%m-%d 格式， 默认`None`​为最新交易日|

**返回值：**​`dataframe`​

|字段名|类型|中文名称|说明|
| ----------------------------| -------| ------------| ----------------------------------------------------------|
|index|str|指数代码|查询成分股的指数代码|
|symbol|str|成分股代码|exchange.sec\_id|
|weight|float|成分股权重|成分股 symbol 对应的指数权重 (中证系列指数不支持该字段）|
|trade\_date|str|交易日期|最新交易日，%Y-%m-%d 格式|
|market\_value\_total|float|总市值|单位：亿元|
|market\_value\_circ|float|流通市值|单位：亿元|

**示例：**

```python
stk_get_index_constituents(index='SHSE.000300')
 
        复制成功
  
```

**输出：**

```python
          index       symbol  weight  trade_date  market_value_total  market_value_circ
0    SHSE.000300  SHSE.600519    0.05  2023-04-18            22083.96           22083.96
1    SHSE.000300  SZSE.300750    0.03  2023-04-18             9989.35            8822.91
2    SHSE.000300  SHSE.601318    0.03  2023-04-18             8887.85            5266.84
3    SHSE.000300  SHSE.600036    0.02  2023-04-18             8998.44            7360.41
4    SHSE.000300  SZSE.000858    0.02  2023-04-18             6921.68            6921.39
5    SHSE.000300  SZSE.000333    0.01  2023-04-18             3972.72            3891.18
6    SHSE.000300  SHSE.601166    0.01  2023-04-18             3616.80            3616.80
7    SHSE.000300  SHSE.600900    0.01  2023-04-18             5030.92            4834.92
8    SHSE.000300  SHSE.601012    0.01  2023-04-18             3033.36            3031.97
9    SHSE.000300  SZSE.300059    0.01  2023-04-18             2859.02            2399.14
10   SHSE.000300  SZSE.002594    0.01  2023-04-18             7248.75            2900.26...
 
        复制成功
  
```

**注意：**

**1.**  数据日频更新，在交易日约 20 点更新当日数据。如果当日数据尚未更新，调用时不指定`trade_date`​会返回前一交易日的成分数据，调用时指定`trade_date`​为当日会返回空 dataframe。

**2.**  `trade_date`​输入非交易日，会返回空 dataframe。`trade_date`​出入的日期格式错误，会报错。

**3.**  指数列表[参考](https://www.myquant.cn/docs2/docs/%E6%8C%87%E6%95%B0.html#%E6%8C%87%E6%95%B0%E6%88%90%E5%88%86%E8%82%A1)

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-balance-pt-%E6%9F%A5%E8%AF%A2%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_balance_pt`​ - 查询资产负债表截面数据（多标的）

查询指定日期截面的股票所属上市公司的资产负债表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_balance_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------------| -------------| ----------| ------| --------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[资产负债表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#balance)**|

**示例：**

```python
stk_get_fundamentals_balance_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='fix_ast', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date        fix_ast  data_type  rpt_type
0  SZSE.000001  2022-10-25  2022-09-30 10975000000.00        102         9
1  SHSE.600000  2022-10-29  2022-09-30 42563000000.00        102         9
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期`date`​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**资产负债表**

|字段名|类型|中文名称|量纲|说明|
| ---------------------------------------| -------| ------------------------------------------------------------| ------| ------------------|
|***流动资产(资产)***|||||
|cash\_bal\_cb|float|现金及存放中央银行款项|元|银行|
|dpst\_ob|float|存放同业款项|元|银行|
|mny\_cptl|float|货币资金|元||
|cust\_cred\_dpst|float|客户信用资金存款|元|证券|
|cust\_dpst|float|客户资金存款|元|证券|
|pm|float|贵金属|元|银行|
|bal\_clr|float|结算备付金|元||
|cust\_rsv|float|客户备付金|元|证券|
|ln\_to\_ob|float|拆出资金|元||
|fair\_val\_fin\_ast|float|以公允价值计量且其变动计入当期损益的金融资产|元||
|ppay|float|预付款项|元||
|fin\_out|float|融出资金|元||
|trd\_fin\_ast|float|交易性金融资产|元||
|deriv\_fin\_ast|float|衍生金融资产|元||
|note\_acct\_rcv|float|应收票据及应收账款|元||
|note\_rcv|float|应收票据|元||
|acct\_rcv|float|应收账款|元||
|acct\_rcv\_fin|float|应收款项融资|元||
|int\_rcv|float|应收利息|元||
|dvd\_rcv|float|应收股利|元||
|oth\_rcv|float|其他应收款|元||
|in\_prem\_rcv|float|应收保费|元||
|rin\_acct\_rcv|float|应收分保账款|元||
|rin\_rsv\_rcv|float|应收分保合同准备金|元|保险|
|rcv\_un\_prem\_rin\_rsv|float|应收分保未到期责任准备金|元||
|rcv\_clm\_rin\_rsv|float|应收分保未决赔偿准备金|元|保险|
|rcv\_li\_rin\_rsv|float|应收分保寿险责任准备金|元|保险|
|rcv\_lt\_hi\_rin\_rsv|float|应收分保长期健康险责任准备金|元|保险|
|ph\_plge\_ln|float|保户质押贷款|元|保险|
|ttl\_oth\_rcv|float|其他应收款合计|元||
|rfd\_dpst|float|存出保证金|元|证券、保险|
|term\_dpst|float|定期存款|元|保险|
|pur\_resell\_fin|float|买入返售金融资产|元||
|aval\_sale\_fin|float|可供出售金融资产|元||
|htm\_inv|float|持有至到期投资|元||
|hold\_for\_sale|float|持有待售资产|元||
|acct\_rcv\_inv|float|应收款项类投资|元|保险|
|invt|float|存货|元||
|contr\_ast|float|合同资产|元||
|ncur\_ast\_one\_y|float|一年内到期的非流动资产|元||
|oth\_cur\_ast|float|其他流动资产|元||
|cur\_ast\_oth\_item|float|流动资产其他项目|元||
|ttl\_cur\_ast|float|流动资产合计|元||
|***非流动资产(资产)***|||||
|loan\_adv|float|发放委托贷款及垫款|元||
|cred\_inv|float|债权投资|元||
|oth\_cred\_inv|float|其他债权投资|元||
|lt\_rcv|float|长期应收款|元||
|lt\_eqy\_inv|float|长期股权投资|元||
|oth\_eqy\_inv|float|其他权益工具投资|元||
|rfd\_cap\_guar\_dpst|float|存出资本保证金|元|保险|
|oth\_ncur\_fin\_ast|float|其他非流动金融资产|元||
|amor\_cos\_fin\_ast\_ncur|float|以摊余成本计量的金融资产（非流动）|元||
|fair\_val\_oth\_inc\_ncur|float|以公允价值计量且其变动计入其他综合收益的金融资产（非流动）|元||
|inv\_prop|float|投资性房地产|元||
|fix\_ast|float|固定资产|元||
|const\_prog|float|在建工程|元||
|const\_matl|float|工程物资|元||
|fix\_ast\_dlpl|float|固定资产清理|元||
|cptl\_bio\_ast|float|生产性生物资产|元||
|oil\_gas\_ast|float|油气资产|元||
|rig\_ast|float|使用权资产|元||
|intg\_ast|float|无形资产|元||
|trd\_seat\_fee|float|交易席位费|元|证券|
|dev\_exp|float|开发支出|元||
|gw|float|商誉|元||
|lt\_ppay\_exp|float|长期待摊费用|元||
|dfr\_tax\_ast|float|递延所得税资产|元||
|oth\_ncur\_ast|float|其他非流动资产|元||
|ncur\_ast\_oth\_item|float|非流动资产其他项目|元||
|ttl\_ncur\_ast|float|非流动资产合计|元||
|oth\_ast|float|其他资产|元|银行、证券、保险|
|ast\_oth\_item|float|资产其他项目|元||
|ind\_acct\_ast|float|独立账户资产|元|保险|
|ttl\_ast|float|资产总计|元||
|***流动负债(负债)***|||||
|brw\_cb|float|向中央银行借款|元||
|dpst\_ob\_fin\_inst|float|同业和其他金融机构存放款项|元|银行、保险|
|ln\_fm\_ob|float|拆入资金|元||
|fair\_val\_fin\_liab|float|以公允价值计量且其变动计入当期损益的金融负债|元||
|sht\_ln|float|短期借款|元||
|adv\_acct|float|预收款项|元||
|contr\_liab|float|合同负债|元||
|trd\_fin\_liab|float|交易性金融负债|元||
|deriv\_fin\_liab|float|衍生金融负债|元||
|sell\_repo\_ast|float|卖出回购金融资产款|元||
|cust\_bnk\_dpst|float|吸收存款|元|银行、保险|
|dpst\_cb\_note\_pay|float|存款证及应付票据|元|银行|
|dpst\_cb|float|存款证|元|银行|
|acct\_rcv\_adv|float|预收账款|元|保险|
|in\_prem\_rcv\_adv|float|预收保费|元|保险|
|fee\_pay|float|应付手续费及佣金|元||
|note\_acct\_pay|float|应付票据及应付账款|元||
|stlf\_pay|float|应付短期融资款|元||
|note\_pay|float|应付票据|元||
|acct\_pay|float|应付账款|元||
|rin\_acct\_pay|float|应付分保账款|元||
|emp\_comp\_pay|float|应付职工薪酬|元||
|tax\_pay|float|应交税费|元||
|int\_pay|float|应付利息|元||
|dvd\_pay|float|应付股利|元||
|ph\_dvd\_pay|float|应付保单红利|元|保险|
|indem\_pay|float|应付赔付款|元|保险|
|oth\_pay|float|其他应付款|元||
|ttl\_oth\_pay|float|其他应付款合计|元||
|ph\_dpst\_inv|float|保户储金及投资款|元|保险|
|in\_contr\_rsv|float|保险合同准备金|元|保险|
|un\_prem\_rsv|float|未到期责任准备金|元|保险|
|clm\_rin\_rsv|float|未决赔款准备金|元|保险|
|li\_liab\_rsv|float|寿险责任准备金|元|保险|
|lt\_hi\_liab\_rsv|float|长期健康险责任准备金|元|保险|
|cust\_bnk\_dpst\_fin|float|吸收存款及同业存放|元||
|inter\_pay|float|内部应付款|元||
|agy\_secu\_trd|float|代理买卖证券款|元||
|agy\_secu\_uw|float|代理承销证券款|元||
|sht\_bnd\_pay|float|应付短期债券|元||
|est\_cur\_liab|float|预计流动负债|元||
|liab\_hold\_for\_sale|float|持有待售负债|元||
|ncur\_liab\_one\_y|float|一年内到期的非流动负债|元||
|oth\_cur\_liab|float|其他流动负债|元||
|cur\_liab\_oth\_item|float|流动负债其他项目|元||
|ttl\_cur\_liab|float|流动负债合计|元||
|***非流动负债（负债）***|||||
|lt\_ln|float|长期借款|元||
|lt\_pay|float|长期应付款|元||
|leas\_liab|float|租赁负债|||
|dfr\_inc|float|递延收益|元||
|dfr\_tax\_liab|float|递延所得税负债|元||
|bnd\_pay|float|应付债券|元||
|bnd\_pay\_pbd|float|其中:永续债|元||
|bnd\_pay\_pfd|float|其中:优先股|元||
|oth\_ncur\_liab|float|其他非流动负债|元||
|spcl\_pay|float|专项应付款|元||
|ncur\_liab\_oth\_item|float|非流动负债其他项目|元||
|lt\_emp\_comp\_pay|float|长期应付职工薪酬|元||
|est\_liab|float|预计负债|元||
|oth\_liab|float|其他负债|元|银行、证券、保险|
|liab\_oth\_item|float|负债其他项目|元|银行、证券、保险|
|ttl\_ncur\_liab|float|非流动负债合计|元||
|ind\_acct\_liab|float|独立账户负债|元|保险|
|ttl\_liab|float|负债合计|元||
|***所有者权益(或股东权益)***|||||
|paid\_in\_cptl|float|实收资本（或股本）|元||
|oth\_eqy|float|其他权益工具|元||
|oth\_eqy\_pfd|float|其中:优先股|元||
|oth\_eqy\_pbd|float|其中:永续债|元||
|oth\_eqy\_oth|float|其中:其他权益工具|元||
|cptl\_rsv|float|资本公积|元||
|treas\_shr|float|库存股|元||
|oth\_comp\_inc|float|其他综合收益|元||
|spcl\_rsv|float|专项储备|元||
|sur\_rsv|float|盈余公积|元||
|rsv\_ord\_rsk|float|一般风险准备|元||
|trd\_risk\_rsv|float|交易风险准备|元|证券|
|ret\_prof|float|未分配利润|元||
|sugg\_dvd|float|建议分派股利|元|银行|
|eqy\_pcom\_oth\_item|float|归属于母公司股东权益其他项目|元||
|ttl\_eqy\_pcom|float|归属于母公司股东权益合计|元||
|min\_sheqy|float|少数股东权益|元||
|sheqy\_oth\_item|float|股东权益其他项目|元||
|ttl\_eqy|float|股东权益合计|元||
|ttl\_liab\_eqy|float|负债和股东权益合计|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-cashflow-pt-%E6%9F%A5%E8%AF%A2%E7%8E%B0%E9%87%91%E6%B5%81%E9%87%8F%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_cashflow_pt`​ - 查询现金流量表截面数据（多标的）

查询指定日期截面的股票所属上市公司的现金流量表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_cashflow_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------------| -------------| ----------| ------| --------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />2-第二季度<br />3-第三季度<br />4-第四季度<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报, 2-第二季度, 3-第三季度, 4-第四季度|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[现金流量表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#cashflow)**|

**示例：**

```python
stk_get_fundamentals_cashflow_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='cash_pay_fee', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  cash_pay_fee
0  SZSE.000001  2022-10-25  2022-09-30         9        102           NaN
1  SHSE.600000  2022-10-29  2022-09-30         9        102 7261000000.00
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期`date`​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**现金流量表**

|字段名|类型|中文名称|量纲|说明|
| ------------------------------------------| -------| -----------------------------------------------------| ------| ------------------|
|***一、经营活动产生的现金流量***|||||
|cash\_rcv\_sale|float|销售商品、提供劳务收到的现金|元||
|net\_incr\_cust\_dpst\_ob|float|客户存款和同业存放款项净增加额|元||
|net\_incr\_cust\_dpst|float|客户存款净增加额|元|银行|
|net\_incr\_dpst\_ob|float|同业及其他金融机构存放款项净增加额|元|银行|
|net\_incr\_brw\_cb|float|向中央银行借款净增加额|元||
|net\_incr\_ln\_fm\_oth|float|向其他金融机构拆入资金净增加额|元||
|cash\_rcv\_orig\_in|float|收到原保险合同保费取得的现金|元||
|net\_cash\_rcv\_rin\_biz|float|收到再保险业务现金净额|元||
|net\_incr\_ph\_dpst\_inv|float|保户储金及投资款净增加额|元||
|net\_decrdpst\_cb\_ob|float|存放中央银行和同业款项及其他金融机构净减少额|元|银行、保险|
|net\_decr\_cb|float|存放中央银行款项净减少额|元|银行|
|net\_decr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净减少额|元|银行|
|net\_cert\_dpst|float|存款证净额|元|银行|
|net\_decr\_trd\_fin|float|交易性金融资产净减少额|元|银行|
|net\_incr\_trd\_liab|float|交易性金融负债净增加额|元|银行|
|cash\_rcv\_int\_fee|float|收取利息、手续费及佣金的现金|元||
|cash\_rcv\_int|float|其中：收取利息的现金|元|银行|
|cash\_rcv\_fee|float|收取手续费及佣金的现金|元|银行|
|net\_incr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净增加额|元|银行|
|net\_incr\_ln\_fm|float|拆入资金净增加额|元||
|net\_incr\_sell\_repo|float|卖出回购金融资产款净增加额|元|银行 保险|
|net\_decr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净减少额|元|银行|
|net\_decr\_ln\_cptl|float|拆出资金净减少额|元|银行、保险|
|net\_dect\_pur\_resell|float|买入返售金融资产净减少额|元|银行、保险|
|net\_incr\_repo|float|回购业务资金净增加额|元||
|net\_decr\_repo|float|回购业务资金净减少额|元|证券|
|tax\_rbt\_rcv|float|收到的税费返还|元||
|net\_cash\_rcv\_trd|float|收到交易性金融资产现金净额|元|保险|
|cash\_rcv\_oth\_oper|float|收到其他与经营活动有关的现金|元||
|net\_cash\_agy\_secu\_trd|float|代理买卖证券收到的现金净额|元|证券|
|cash\_rcv\_pur\_resell|float|买入返售金融资产收到的现金|元|证券、保险|
|net\_cash\_agy\_secu\_uw|float|代理承销证券收到的现金净额|元|证券|
|cash\_rcv\_dspl\_debt|float|处置抵债资产收到的现金|元|银行|
|canc\_loan\_rcv|float|收回的已于以前年度核销的贷款|元|银行|
|cf\_in\_oper|float|经营活动现金流入小计|元||
|cash\_pur\_gds\_svc|float|购买商品、接受劳务支付的现金|元||
|net\_incr\_ln\_adv\_cust|float|客户贷款及垫款净增加额|元||
|net\_decr\_brw\_cb|float|向中央银行借款净减少额|元|银行|
|net\_incr\_dpst\_cb\_ob|float|存放中央银行和同业款项净增加额|元||
|net\_incr\_cb|float|存放中央银行款项净增加额|元|银行|
|net\_incr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净增加额|元|银行|
|net\_decr\_dpst\_ob|float|同业及其他机构存放款减少净额|元|银行|
|net\_decr\_issu\_cert\_dpst|float|已发行存款证净减少额|元|银行|
|net\_incr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净增加额|元|银行|
|net\_incr\_ln\_to|float|拆出资金净增加额|元|银行、保险|
|net\_incr\_pur\_resell|float|买入返售金融资产净增加额|元|银行、保险|
|net\_decr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净减少额|元|银行|
|net\_decr\_ln\_fm|float|拆入资金净减少额|元|银行、证券|
|net\_decr\_sell\_repo|float|卖出回购金融资产净减少额|元|银行、保险|
|net\_incr\_trd\_fin|float|交易性金融资产净增加额|元|银行|
|net\_decr\_trd\_liab|float|交易性金融负债净减少额|元|银行|
|cash\_pay\_indem\_orig|float|支付原保险合同赔付款项的现金|元||
|net\_cash\_pay\_rin\_biz|float|支付再保险业务现金净额|元|保险|
|cash\_pay\_int\_fee|float|支付利息、手续费及佣金的现金|元||
|cash\_pay\_int|float|其中：支付利息的现金|元|银行|
|cash\_pay\_fee|float|支付手续费及佣金的现金|元|银行|
|ph\_dvd\_pay|float|支付保单红利的现金|元||
|net\_decr\_ph\_dpst\_inv|float|保户储金及投资款净减少额|元|保险|
|cash\_pay\_emp|float|支付给职工以及为职工支付的现金|||
|cash\_pay\_tax|float|支付的各项税费|元||
|net\_cash\_pay\_trd|float|支付交易性金融资产现金净额|元|保险|
|cash\_pay\_oth\_oper|float|支付其他与经营活动有关的现金|元||
|net\_incr\_dspl\_trd\_fin|float|处置交易性金融资产净增加额|元||
|cash\_pay\_fin\_leas|float|购买融资租赁资产支付的现金|元|银行|
|net\_decr\_agy\_secu\_pay|float|代理买卖证券支付的现金净额（净减少额）|元|证券|
|net\_decr\_dspl\_trd\_fin|float|处置交易性金融资产的净减少额|元|证券|
|cf\_out\_oper|float|经营活动现金流出小计|元||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|***二、投资活动产生的现金流量：***|||||
|cash\_rcv\_sale\_inv|float|收回投资收到的现金|元||
|inv\_inc\_rcv|float|取得投资收益收到的现金|元||
|cash\_rcv\_dvd\_prof|float|分得股利或利润所收到的现金|元|银行|
|cash\_rcv\_dspl\_ast|float|处置固定资产、无形资产和其他长期资产收回的现金净额|元||
|cash\_rcv\_dspl\_sub\_oth|float|处置子公司及其他营业单位收到的现金净额|元||
|cash\_rcv\_oth\_inv|float|收到其他与投资活动有关的现金|元||
|cf\_in\_inv|float|投资活动现金流入小计|元||
|pur\_fix\_intg\_ast|float|购建固定资产、无形资产和其他长期资产支付的现金|元||
|cash\_out\_dspl\_sub\_oth|float|处置子公司及其他营业单位流出的现金净额|元|保险|
|cash\_pay\_inv|float|投资支付的现金|元||
|net\_incr\_ph\_plge\_ln|float|保户质押贷款净增加额|元|保险|
|add\_cash\_pled\_dpst|float|增加质押和定期存款所支付的现金|元||
|net\_incr\_plge\_ln|float|质押贷款净增加额|元||
|net\_cash\_get\_sub|float|取得子公司及其他营业单位支付的现金净额|元||
|net\_pay\_pur\_resell|float|支付买入返售金融资产现金净额|元|证券、保险|
|cash\_pay\_oth\_inv|float|支付其他与投资活动有关的现金|元||
|cf\_out\_inv|float|投资活动现金流出小计|||
|net\_cf\_inv|float|投资活动产生的现金流量净额|元||
|***三、筹资活动产生的现金流量：***|||||
|cash\_rcv\_cptl|float|吸收投资收到的现金|元||
|sub\_rcv\_ms\_inv|float|其中：子公司吸收少数股东投资收到的现金|元||
|brw\_rcv|float|取得借款收到的现金|元||
|cash\_rcv\_bnd\_iss|float|发行债券收到的现金|元||
|net\_cash\_rcv\_sell\_repo|float|收到卖出回购金融资产款现金净额|元|保险|
|cash\_rcv\_oth\_fin|float|收到其他与筹资活动有关的现金|元||
|issu\_cert\_dpst|float|发行存款证|元|银行|
|cf\_in\_fin\_oth|float|筹资活动现金流入其他项目|元||
|cf\_in\_fin|float|筹资活动现金流入小计|元||
|cash\_rpay\_brw|float|偿还债务支付的现金|元||
|cash\_pay\_bnd\_int|float|偿付债券利息支付的现金|元|银行|
|cash\_pay\_dvd\_int|float|分配股利、利润或偿付利息支付的现金|元||
|sub\_pay\_dvd\_prof|float|其中：子公司支付给少数股东的股利、利润|元||
|cash\_pay\_oth\_fin|float|支付其他与筹资活动有关的现金|元||
|net\_cash\_pay\_sell\_repo|float|支付卖出回购金融资产款现金净额|元|保险|
|cf\_out\_fin|float|筹资活动现金流出小计|元||
|net\_cf\_fin|float|筹资活动产生的现金流量净额|元||
|efct\_er\_chg\_cash|float|四、汇率变动对现金及现金等价物的影响|元||
|net\_incr\_cash\_eq|float|五、现金及现金等价物净增加额|元||
|cash\_cash\_eq\_bgn|float|加：期初现金及现金等价物余额|元||
|cash\_cash\_eq\_end|float|六、期末现金及现金等价物余额|元||
|***补充资料 1．将净利润调节为经营活动现金流量：***|||||
|net\_prof|float|净利润|元||
|ast\_impr|float|资产减值准备|元||
|accr\_prvs\_ln\_impa|float|计提贷款减值准备|元|银行|
|accr\_prvs\_oth\_impa|float|计提其他资产减值准备|元|银行|
|accr\_prem\_rsv|float|提取的保险责任准备金|元|保险|
|accr\_unearn\_prem\_rsv|float|提取的未到期的责任准备金|元|保险|
|defr\_fix\_prop|float|固定资产和投资性房地产折旧|元||
|depr\_oga\_cba|float|其中:固定资产折旧、油气资产折耗、生产性生物资产折旧|元||
|amor\_intg\_ast\_lt\_exp|float|无形资产及长期待摊费用等摊销|元|银行、证券、保险|
|amort\_intg\_ast|float|无形资产摊销|元||
|amort\_lt\_exp\_ppay|float|长期待摊费用摊销|元||
|dspl\_ast\_loss|float|处置固定资产、无形资产和其他长期资产的损失|元||
|fair\_val\_chg\_loss|float|固定资产报废损失|元||
|fv\_chg\_loss|float|公允价值变动损失|元||
|dfa|float|固定资产折旧|元|银行|
|fin\_exp|float|财务费用|元||
|inv\_loss|float|投资损失|元||
|exchg\_loss|float|汇兑损失|元|银行、证券、保险|
|dest\_incr|float|存款的增加|元|银行|
|loan\_decr|float|贷款的减少|元|银行|
|cash\_pay\_bnd\_int\_iss|float|发行债券利息支出|元|银行|
|dfr\_tax|float|递延所得税|元||
|dfr\_tax\_ast\_decr|float|其中:递延所得税资产减少|元||
|dfr\_tax\_liab\_incr|float|递延所得税负债增加|元||
|invt\_decr|float|存货的减少|元||
|decr\_rcv\_oper|float|经营性应收项目的减少|元||
|incr\_pay\_oper|float|经营性应付项目的增加|元||
|oth|float|其他|元||
|cash\_end|float|现金的期末余额|元||
|cash\_bgn|float|减：现金的期初余额|元||
|cash\_eq\_end|float|加:现金等价物的期末余额|元||
|cash\_eq\_bgn|float|减:现金等价物的期初余额|元||
|cred\_impr\_loss|float|信用减值损失|元||
|est\_liab\_add|float|预计负债的增加|元||
|dr\_cnv\_cptl|float|债务转为资本|元||
|cptl\_bnd\_expr\_one\_y|float|一年内到期的可转换公司债券|元||
|fin\_ls\_fix\_ast|float|融资租入固定资产|元||
|amort\_dfr\_inc|float|递延收益摊销|元||
|depr\_inv\_prop|float|投资性房地产折旧|元||
|trd\_fin\_decr|float|交易性金融资产的减少|元|证券、保险|
|im\_net\_cf\_oper|float|间接法-经营活动产生的现金流量净额|元||
|im\_net\_incr\_cash\_eq|float|间接法-现金及现金等价物净增加额|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-income-pt-%E6%9F%A5%E8%AF%A2%E5%88%A9%E6%B6%A6%E8%A1%A8%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_fundamentals_income_pt`​ - 查询利润表截面数据（多标的）

查询指定日期截面的股票所属上市公司的利润表数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_income_pt(symbols, rpt_type=None, data_type=None, date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------------| -------------| ----------| ------| --------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />2-第二季度<br />3-第三季度<br />4-第四季度<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报, 2-第二季度, 3-第三季度, 4-第四季度|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[利润表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#income)**|

**示例：**

```python
stk_get_fundamentals_income_pt(symbols='SHSE.600000, SZSE.000001', rpt_type=None, data_type=None, date='2022-10-01', fields='inc_oper', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type        inc_oper
0  SZSE.000001  2022-10-25  2022-09-30         9        102 138265000000.00
1  SHSE.600000  2022-10-29  2022-09-30         9        102 143680000000.00
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期`date`​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**利润表**

|字段名|类型|中文名称|量纲|说明|
| ----------------------------------------| -------| --------------------------------------| ------| ------------------|
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|net\_inc\_int|float|利息净收入|元|证券、银行、保险|
|exp\_int|float|利息支出|元||
|net\_inc\_fee\_comm|float|手续费及佣金净收入|元|证券、银行|
|inc\_rin\_prem|float|其中：分保费收入|元|保险|
|net\_inc\_secu\_agy|float|其中:代理买卖证券业务净收入|元|证券|
|inc\_fee\_comm|float|手续费及佣金收入|元||
|in\_prem\_earn|float|已赚保费|元|保险|
|inc\_in\_biz|float|其中:保险业务收入|元|保险|
|rin\_prem\_cede|float|分出保费|元|保险|
|unear\_prem\_rsv|float|提取未到期责任准备金|元|保险|
|net\_inc\_uw|float|证券承销业务净收入|元|证券|
|net\_inc\_cust\_ast\_mgmt|float|受托客户资产管理业务净收入|元|证券|
|inc\_fx|float|汇兑收益|元||
|inc\_other\_oper|float|其他业务收入|元||
|inc\_oper\_balance|float|营业收入平衡项目|元||
|ttl\_inc\_oper\_other|float|营业总收入其他项目|元||
|ttl\_cost\_oper|float|营业总成本|元||
|cost\_oper|float|营业成本|元||
|exp\_oper|float|营业支出|元|证券、银行、保险|
|biz\_tax\_sur|float|营业税金及附加|元||
|exp\_sell|float|销售费用|元||
|exp\_adm|float|管理费用|元||
|exp\_rd|float|研发费用|元||
|exp\_fin|float|财务费用|元||
|int\_fee|float|其中:利息费用|元||
|inc\_int|float|利息收入|元||
|exp\_oper\_adm|float|业务及管理费|元|证券、银行、保险|
|exp\_rin|float|减:摊回分保费用|元|保险|
|rfd\_prem|float|退保金|元|保险|
|comp\_pay|float|赔付支出|元|保险|
|rin\_clm\_pay|float|减:摊回赔付支出|元|保险|
|draw\_insur\_liab|float|提取保险责任准备金|元|保险|
|amor\_insur\_liab|float|减:摊回保险责任准备金|元|保险|
|exp\_ph\_dvd|float|保单红利支出|元|保险|
|exp\_fee\_comm|float|手续费及佣金支出|元||
|other\_oper\_cost|float|其他业务成本|元||
|oper\_exp\_balance|float|营业支出平衡项目|元|证券、银行、保险|
|exp\_oper\_other|float|营业支出其他项目|元|证券、银行、保险|
|ttl\_cost\_oper\_other|float|营业总成本其他项目|元||
|***其他经营收益***|||元||
|inc\_inv|float|投资收益|元||
|inv\_inv\_jv\_p|float|对联营企业和合营企业的投资收益|元||
|inc\_ast\_dspl|float|资产处置收益|元||
|ast\_impr\_loss|float|资产减值损失(新)|元||
|cred\_impr\_loss|float|信用减值损失(新)|元||
|inc\_fv\_chg|float|公允价值变动收益|元||
|inc\_other|float|其他收益|元||
|oper\_prof\_balance|float|营业利润平衡项目|元||
|oper\_prof|float|营业利润|元||
|inc\_noper|float|营业外收入|元||
|exp\_noper|float|营业外支出|元||
|ttl\_prof\_balance|float|利润总额平衡项目|元||
|oper\_prof\_other|float|营业利润其他项目|元||
|ttl\_prof|float|利润总额|元||
|inc\_tax|float|所得税费用|元||
|net\_prof|float|净利润|元||
|oper\_net\_prof|float|持续经营净利润|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|min\_int\_inc|float|少数股东损益|元||
|end\_net\_prof|float|终止经营净利润|元||
|net\_prof\_other|float|净利润其他项目|元||
|eps\_base|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|other\_comp\_inc|float|其他综合收益|元||
|other\_comp\_inc\_pcom|float|归属于母公司股东的其他综合收益|元||
|other\_comp\_inc\_min|float|归属于少数股东的其他综合收益|元||
|ttl\_comp\_inc|float|综合收益总额|元||
|ttl\_comp\_inc\_pcom|float|归属于母公司所有者的综合收益总额|元||
|ttl\_comp\_inc\_min|float|归属于少数股东的综合收益总额|元||
|prof\_pre\_merge|float|被合并方在合并前实现利润|元||
|net\_rsv\_in\_contr|float|提取保险合同准备金净额|元||
|net\_pay\_comp|float|赔付支出净额|元||
|net\_loss\_ncur\_ast|float|非流动资产处置净损失|元||
|amod\_fin\_asst\_end|float|以摊余成本计量的金融资产终止确认收益|元||
|cash\_flow\_hedging\_pl|float|现金流量套期损益的有效部分|元||
|cur\_trans\_diff|float|外币财务报表折算差额|元||
|gain\_ncur\_ast|float|非流动资产处置利得|元||
|afs\_fv\_chg\_pl|float|可供出售金融资产公允价值变动损益|元||
|oth\_eqy\_inv\_fv\_chg|float|其他权益工具投资公允价值变动|元||
|oth\_debt\_inv\_fv\_chg|float|其他债权投资公允价值变动|元||
|oth\_debt\_inv\_cred\_impr|float|其他债权投资信用减值准备|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-prime-pt-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E4%B8%BB%E8%A6%81%E6%8C%87%E6%A0%87%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_finance_prime_pt`​ - 查询财务主要指标截面数据（多标的）

查询指定日期截面上，股票所属上市公司的财务主要指标数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_prime_pt(symbols, fields, rpt_type=None, data_type=None, date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------------| -------------| ----------| ------| --------| -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务主要指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />100-合并最初（未修正的合并原始）<br />101-合并原始<br />102-合并调整<br />200-母公司最初（未修正的母公司原始）<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始，如果合并原始未修正返回合并最初|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：100-合并最初（未修正的合并原始） 101-合并原始 102-合并调整 201-母公司原始 202-母公司调整 200-母公司最初（未修正的母公司原始）|
|fields|list[float]|财务字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[财务主要指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#main_financial)**|

**示例：**

```python
stk_get_finance_prime_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='eps_basic,eps_dil', rpt_type=None, data_type=None, date='2023-06-19', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  eps_dil  eps_basic
0  SZSE.000001  2023-04-25  2023-03-31         1        101   0.6500     0.6500
1  SZSE.300002  2023-04-27  2023-03-31         1        101   0.0914     0.0914
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期`date`​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**财务主要指标**

|字段名|类型|中文名称|量纲|说明|
| --------------------------------| -------| --------------------------------------------| ------| ------|
|eps\_basic|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|eps\_basic\_cut|float|扣除非经常性损益后的基本每股收益|元||
|eps\_dil\_cut|float|扣除非经常性损益后的稀释每股收益|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|bps\_pcom\_ps|float|归属于母公司股东的每股净资产|元||
|ttl\_ast|float|总资产|元||
|ttl\_liab|float|总负债|元||
|share\_cptl|float|股本|股||
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|oper\_prof|float|营业利润|元||
|ttl\_prof|float|利润总额|元||
|ttl\_eqy\_pcom|float|归属于母公司股东的所有者权益|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|net\_prof\_pcom\_cut|float|扣除非经常性损益后归属于母公司股东的净利润|元||
|roe|float|全面摊薄净资产收益率|%||
|roe\_weight\_avg|float|加权平均净资产收益率|%||
|roe\_cut|float|扣除非经常性损益后的全面摊薄净资产收益率|%||
|roe\_weight\_avg\_cut|float|扣除非经常性损益后的加权平均净资产收益率|%||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|eps\_yoy|float|每股收益同比比例|%||
|inc\_oper\_yoy|float|营业收入同比比例|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比比例|%||
|net\_prof\_pcom\_yoy|float|归母净利润同比比例|%||
|bps\_sh|float|归属于普通股东的每股净资产|元||
|net\_asset|float|归属于普通股东的净资产|元||
|net\_prof|float|归属于普通股东的净利润|元||
|net\_prof\_cut|float|扣除非经常性损益后归属于普通股股东的净利润|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-deriv-pt-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E8%A1%8D%E7%94%9F%E6%8C%87%E6%A0%87%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_finance_deriv_pt`​ - 查询财务衍生指标截面数据（多标的）

查询指定日期截面上，股票所属上市公司的财务衍生指标数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_deriv_pt(symbols, fields, rpt_type=None, data_type=None, date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ---------------| -------------| ----------| ------| --------| -------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的财务衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。 101-合并原始<br />102-合并调整 201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|date|str|查询日期|N|None|查询时间，时间类型为发布日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|距查询日期最近的发布日期<br />若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期<br />若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期<br />若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[财务衍生指标指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#financial_derivative)**|

**示例：**

```python
stk_get_finance_deriv_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='eps_basic,eps_dil2',
                                   rpt_type=None, data_type=None, date='2023-06-19', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  ...  data_type  eps_basic  eps_dil2
0  SZSE.000001  2023-04-25  2023-03-31  ...        102     0.6500    0.6500
1  SZSE.300002  2023-04-27  2023-03-31  ...        102     0.0914    0.0914
 
        复制成功
  
```

**注意：**

**1.**  为避免未来数据问题，指定查询日期`date`​后，返回发布日期小于查询日期下的最新报告日期数据。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**财务衍生指标指标**

|字段名|类型|中文名称|量纲|说明|
| ------------------------------------------------| -------| ----------------------------------------------------| ------| ------|
|eps\_basic|float|每股收益EPS(基本)|元||
|eps\_dil2|float|每股收益EPS(稀释)|元||
|eps\_dil|float|每股收益EPS(期末股本摊薄)|元||
|eps\_basic\_cut|float|每股收益EPS(扣除/基本)|元||
|eps\_dil2\_cut|float|每股收益EPS(扣除/稀释)|元||
|eps\_dil\_cut|float|每股收益EPS(扣除/期末股本摊薄)|元||
|bps|float|每股净资产BPS|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|ttl\_inc\_oper\_ps|float|每股营业总收入|元||
|inc\_oper\_ps|float|每股营业收入|元||
|ebit\_ps|float|每股息税前利润|元||
|cptl\_rsv\_ps|float|每股资本公积|元||
|sur\_rsv\_ps|float|每股盈余公积|元||
|retain\_prof\_ps|float|每股未分配利润|元||
|retain\_inc\_ps|float|每股留存收益|元||
|net\_cf\_ps|float|每股现金流量净额|元||
|fcff\_ps|float|每股企业自由现金流量|元||
|fcfe\_ps|float|每股股东自由现金流量|元||
|ebitda\_ps|float|每股EBITDA|元||
|roe|float|净资产收益率ROE(摊薄)|%||
|roe\_weight|float|净资产收益率ROE(加权)|%||
|roe\_avg|float|净资产收益率ROE(平均)|%||
|roe\_cut|float|净资产收益率ROE(扣除/摊薄)|%||
|roe\_weight\_cut|float|净资产收益率ROE(扣除/加权)|%||
|ocf\_toi|float|经营性现金净流量/营业总收入|||
|eps\_dil\_yoy|float|稀释每股收益同比增长率|%||
|net\_cf\_oper\_ps\_yoy|float|每股经营活动中产生的现金流量净额同比增长率|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比增长率|%||
|inc\_oper\_yoy|float|营业收入同比增长率|%||
|oper\_prof\_yoy|float|营业利润同比增长率|%||
|ttl\_prof\_yoy|float|利润总额同比增长率|%||
|net\_prof\_pcom\_yoy|float|归属母公司股东的净利润同比增长率|%||
|net\_prof\_pcom\_cut\_yoy|float|归属母公司股东的净利润同比增长率(扣除非经常性损益)|%||
|net\_cf\_oper\_yoy|float|经营活动产生的现金流量净额同比增长率|%||
|roe\_yoy|float|净资产收益率同比增长率(摊薄)|%||
|net\_asset\_yoy|float|净资产同比增长率|%||
|ttl\_liab\_yoy|float|总负债同比增长率|%||
|ttl\_asset\_yoy|float|总资产同比增长率|%||
|net\_cash\_flow\_yoy|float|现金净流量同比增长率|%||
|bps\_gr\_begin\_year|float|每股净资产相对年初增长率|%||
|ttl\_asset\_gr\_begin\_year|float|资产总计相对年初增长率|%||
|ttl\_eqy\_pcom\_gr\_begin\_year|float|归属母公司的股东权益相对年初增长率|%||
|net\_debt\_eqy\_ev|float|净债务/股权价值|%||
|int\_debt\_eqy\_ev|float|带息债务/股权价值|||
|eps\_bas\_yoy|float|基本每股收益同比增长率|%||
|ebit|float|EBIT(正推法)|元||
|ebitda|float|EBITDA(正推法)|元||
|ebit\_inverse|float|EBIT(反推法)|元||
|ebitda\_inverse|float|EBITDA(反推法)|元||
|nr\_prof\_loss|float|非经常性损益|元||
|net\_prof\_cut|float|扣除非经常性损益后的净利润|元||
|gross\_prof|float|毛利润|元||
|oper\_net\_inc|float|经营活动净收益|元||
|val\_chg\_net\_inc|float|价值变动净收益|元||
|exp\_rd|float|研发费用|元||
|ttl\_inv\_cptl|float|全部投入资本|元||
|work\_cptl|float|营运资本|元||
|net\_work\_cptl|float|净营运资本|元||
|tg\_asset|float|有形资产|元||
|retain\_inc|float|留存收益|元||
|int\_debt|float|带息债务|元||
|net\_debt|float|净债务|元||
|curr\_liab\_non\_int|float|无息流动负债|元||
|ncur\_liab\_non\_int|float|无息非流动负债|元||
|fcff|float|企业自由现金流量FCFF|元||
|fcfe|float|股权自由现金流量FCFE|元||
|cur\_depr\_amort|float|当期计提折旧与摊销|元||
|eqy\_mult\_dupont|float|权益乘数(杜邦分析)|||
|net\_prof\_pcom\_np|float|归属母公司股东的净利润/净利润|%||
|net\_prof\_tp|float|净利润/利润总额|%||
|ttl\_prof\_ebit|float|利润总额/息税前利润|%||
|roe\_cut\_avg|float|净资产收益率ROE(扣除/平均)|%||
|roe\_add|float|净资产收益率ROE(增发条件)|%||
|roe\_ann|float|净资产收益率ROE(年化)|%||
|roa|float|总资产报酬率ROA|%||
|roa\_ann|float|总资产报酬率ROA(年化)|%||
|jroa|float|总资产净利率|%||
|jroa\_ann|float|总资产净利率(年化)|%||
|roic|float|投入资本回报率ROIC|%||
|sale\_npm|float|销售净利率|%||
|sale\_gpm|float|销售毛利率|%||
|sale\_cost\_rate|float|销售成本率|%||
|sale\_exp\_rate|float|销售期间费用率|%||
|net\_prof\_toi|float|净利润/营业总收入|%||
|oper\_prof\_toi|float|营业利润/营业总收入|%||
|ebit\_toi|float|息税前利润/营业总收入|%||
|ttl\_cost\_oper\_toi|float|营业总成本/营业总收入|%||
|exp\_oper\_toi|float|营业费用/营业总收入|%||
|exp\_admin\_toi|float|管理费用/营业总收入|%||
|exp\_fin\_toi|float|财务费用/营业总收入|%||
|ast\_impr\_loss\_toi|float|资产减值损失/营业总收入|%||
|ebitda\_toi|float|EBITDA/营业总收入|%||
|oper\_net\_inc\_tp|float|经营活动净收益/利润总额|%||
|val\_chg\_net\_inc\_tp|float|价值变动净收益/利润总额|%||
|net\_exp\_noper\_tp|float|营业外支出净额/利润总额|||
|inc\_tax\_tp|float|所得税/利润总额|%||
|net\_prof\_cut\_np|float|扣除非经常性损益的净利润/净利润|%||
|eqy\_mult|float|权益乘数|||
|curr\_ast\_ta|float|流动资产/总资产|%||
|ncurr\_ast\_ta|float|非流动资产/总资产|%||
|tg\_ast\_ta|float|有形资产/总资产|%||
|ttl\_eqy\_pcom\_tic|float|归属母公司股东的权益/全部投入资本|%||
|int\_debt\_tic|float|带息负债/全部投入资本|%||
|curr\_liab\_tl|float|流动负债/负债合计|%||
|ncurr\_liab\_tl|float|非流动负债/负债合计|%||
|ast\_liab\_rate|float|资产负债率|%||
|quick\_rate|float|速动比率|||
|curr\_rate|float|流动比率|||
|cons\_quick\_rate|float|保守速动比率|||
|liab\_eqy\_rate|float|产权比率|||
|ttl\_eqy\_pcom\_tl|float|归属母公司股东的权益/负债合计|||
|ttl\_eqy\_pcom\_debt|float|归属母公司股东的权益/带息债务|||
|tg\_ast\_tl|float|有形资产/负债合计|||
|tg\_ast\_int\_debt|float|有形资产/带息债务|||
|tg\_ast\_net\_debt|float|有形资产/净债务|||
|ebitda\_tl|float|息税折旧摊销前利润/负债合计|||
|net\_cf\_oper\_tl|float|经营活动产生的现金流量净额/负债合计|||
|net\_cf\_oper\_int\_debt|float|经营活动产生的现金流量净额/带息债务|||
|net\_cf\_oper\_curr\_liab|float|经营活动产生的现金流量净额/流动负债|||
|net\_cf\_oper\_net\_liab|float|经营活动产生的现金流量净额/净债务|||
|ebit\_int\_cover|float|已获利息倍数|||
|long\_liab\_work\_cptl|float|长期债务与营运资金比率|||
|ebitda\_int\_debt|float|EBITDA/带息债务|%||
|oper\_cycle|float|营业周期|天||
|inv\_turnover\_days|float|存货周转天数|天||
|acct\_rcv\_turnover\_days|float|应收账款周转天数(含应收票据)|天||
|inv\_turnover\_rate|float|存货周转率|次||
|acct\_rcv\_turnover\_rate|float|应收账款周转率(含应收票据)|次||
|curr\_ast\_turnover\_rate|float|流动资产周转率|次||
|fix\_ast\_turnover\_rate|float|固定资产周转率|次||
|ttl\_ast\_turnover\_rate|float|总资产周转率|次||
|cash\_rcv\_sale\_oi|float|销售商品提供劳务收到的现金/营业收入|%||
|net\_cf\_oper\_oi|float|经营活动产生的现金流量净额/营业收入|%||
|net\_cf\_oper\_oni|float|经营活动产生的现金流量净额/经营活动净收益|||
|cptl\_exp\_da|float|资本支出/折旧摊销|%||
|cash\_rate|float|现金比率|||
|acct\_pay\_turnover\_days|float|应付账款周转天数(含应付票据)|天||
|acct\_pay\_turnover\_rate|float|应付账款周转率(含应付票据)|次||
|net\_oper\_cycle|float|净营业周期|天||
|ttl\_cost\_oper\_yoy|float|营业总成本同比增长率|%||
|net\_prof\_yoy|float|净利润同比增长率|%||
|net\_cf\_oper\_np|float|经营活动产生的现金流量净额/净利润|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-valuation-pt-%E6%9F%A5%E8%AF%A2%E4%BC%B0%E5%80%BC%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_valuation_pt`​ - 查询估值指标单日截面数据（多标的）

查询指定日期截面上，股票的单日估值指标（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_valuation_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| -------------| ----------| ------| --------| -------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[估值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#valuation)**|

**示例：**

```python
stk_get_daily_valuation_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='pe_ttm,pe_lyr,pe_mrq',
                               trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date   pe_ttm   pe_mrq   pe_lyr
0  SZSE.000001  2023-06-26   4.5900   3.7145   4.7666
1  SZSE.300002  2023-06-26  39.3144  36.2480  47.6621
 
        复制成功
  
```

**注意：**

**1.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**估值指标**

|字段名|类型|中文名称|量纲|说明|
| ----------------------| -------| --------------------------------------------| ------| ------|
|pe\_ttm|float|市盈率(TTM)|倍||
|pe\_lyr|float|市盈率(最新年报LYR)|倍||
|pe\_mrq|float|市盈率(最新报告期MRQ)|倍||
|pe\_1q|float|市盈率(当年一季×4)|倍||
|pe\_2q|float|市盈率(当年中报×2)|倍||
|pe\_3q|float|市盈率(当年三季×4/3)|倍||
|pe\_ttm\_cut|float|市盈率(TTM) 扣除非经常性损益|倍||
|pe\_lyr\_cut|float|市盈率(最新年报LYR) 扣除非经常性损益|倍||
|pe\_mrq\_cut|float|市盈率(最新报告期MRQ) 扣除非经常性损益|倍||
|pe\_1q\_cut|float|市盈率(当年一季×4) 扣除非经常性损益|倍||
|pe\_2q\_cut|float|市盈率(当年中报×2) 扣除非经常性损益|倍||
|pe\_3q\_cut|float|市盈率(当年三季×4/3) 扣除非经常性损益|倍||
|pb\_lyr|float|市净率(最新年报LYR)|倍||
|pb\_mrq|float|市净率(最新报告期MRQ)|倍||
|pb\_lyr\_1|float|市净率(剔除其他权益工具，最新年报LYR)|倍||
|pb\_mrq\_1|float|市净率(剔除其他权益工具，最新报告期MRQ)|倍||
|pcf\_ttm\_oper|float|市现率(经营现金流,TTM)|倍||
|pcf\_ttm\_ncf|float|市现率(现金净流量,TTM)|倍||
|pcf\_lyr\_oper|float|市现率(经营现金流,最新年报LYR)|倍||
|pcf\_lyr\_ncf|float|市现率(现金净流量,最新年报LYR)|倍||
|ps\_ttm|float|市销率(TTM)|倍||
|ps\_lyr|float|市销率(最新年报LYR)|倍||
|ps\_mrq|float|市销率(最新报告期MRQ)|倍||
|ps\_1q|float|市销率(当年一季×4)|倍||
|ps\_2q|float|市销率(当年中报×2)|倍||
|ps\_3q|float|市销率(当年三季×4/3)|倍||
|peg\_lyr|float|历史PEG值(当年年报增长率)|||
|peg\_mrq|float|历史PEG值(最新报告期增长率)|||
|peg\_1q|float|历史PEG值(当年1季\*4较上年年报增长率)|||
|peg\_2q|float|历史PEG值(当年中报\*2较上年年报增长率)|||
|peg\_3q|float|历史PEG值(当年3季\*4/3较上年年报增长率)|||
|peg\_np\_cgr|float|历史PEG值(PE\_TTM较净利润3年复合增长率)|||
|peg\_npp\_cgr|float|历史PEG值(PE\_TTM较净利润3年复合增长率)|||
|dy\_ttm|float|股息率(滚动 12 月TTM)|%||
|dy\_lfy|float|股息率(上一财年LFY)|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-mktvalue-pt-%E6%9F%A5%E8%AF%A2%E5%B8%82%E5%80%BC%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_mktvalue_pt`​ - 查询市值指标单日截面数据（多标的）

查询指定日期截面上，股票的单日市值截面数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_mktvalue_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| -------------| ----------| ------| --------| -------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[市值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#mktvalue)**|

**示例：**

```python
stk_get_daily_mktvalue_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='tot_mv,tot_mv_csrc,a_mv',
                               trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date        a_mv      tot_mv  tot_mv_csrc
0  SZSE.000001  2023-06-26  2.1696e+11  2.1696e+11   2.1696e+11
1  SZSE.300002  2023-06-26  2.5828e+10  2.5828e+10   2.5828e+10
 
        复制成功
  
```

**注意：**

**1.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**市值指标**

|字段名|类型|中文名称|量纲|说明|
| -------------------------| -------| -----------------------------------| ------| ------|
|tot\_mv|float|总市值|元||
|tot\_mv\_csrc|float|总市值(证监会算法)|元||
|a\_mv|float|A股流通市值(含限售股)|元||
|a\_mv\_ex\_ltd|float|A股流通市值(不含限售股)|元||
|b\_mv|float|B股流通市值(含限售股，折人民币)|元||
|b\_mv\_ex\_ltd|float|B股流通市值(不含限售股，折人民币)|元||
|ev|float|企业价值(含货币资金)(EV1)|元||
|ev\_ex\_curr|float|企业价值(剔除货币资金)(EV2)|元||
|ev\_ebitda|float|企业倍数|倍||
|equity\_value|float|股权价值|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-basic-pt-%E6%9F%A5%E8%AF%A2%E8%82%A1%E6%9C%AC%E7%AD%89%E5%9F%BA%E7%A1%80%E6%8C%87%E6%A0%87%E5%8D%95%E6%97%A5%E6%88%AA%E9%9D%A2%E6%95%B0%E6%8D%AE-%E5%A4%9A%E6%A0%87%E7%9A%84)​`stk_get_daily_basic_pt`​ - 查询股本等基础指标单日截面数据（多标的）

查询指定日期截面上，股票的单日基础指标截面数据（point-in-time）

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_basic_pt(symbols, fields, trade_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| -------------| ----------| ------| --------| -------------------------------------------------------------------------------------------------------------------------------------------------|
|symbols|str or list|股票代码|Y|无|必填，可输入多个，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86) 采用 str 格式时，多个标的代码必须用**英文逗号**分割，如：`'SHSE.600008,SZSE.000002'`​ 采用 list 格式时，多个标的代码示例：['SHSE.600008', 'SZSE.000002']|
|fields|str|返回字段|Y|无|指定需要返回的交易衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|trade\_date|str|查询日期|N|None|查询时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定查询 `fields`​字段的数值. 支持的字段名请参考 **[基础指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#daily_basic)**|

**示例：**

```python
stk_get_daily_basic_pt(symbols=['SZSE.000001', 'SZSE.300002'], fields='tclose,turnrate,ttl_shr',
                                  trade_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  turnrate  tclose     ttl_shr
0  SZSE.000001  2023-06-27    0.2379   11.28  1.9406e+10
1  SZSE.300002  2023-06-27    7.3596   13.44  1.9611e+09
 
        复制成功
  
```

**注意：**

**1.**  如果`fields`​参数的财务字段填写不正确，或填写空字段`""`​，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**基础指标**

|字段名|类型|中文名称|量纲|说明|
| ---------------------| -------| ----------------------------------------------------------------| ------| ------|
|tclose|float|收盘价|元||
|turnrate|float|当日换手率|%||
|ttl\_shr|float|总股本|股||
|circ\_shr|float|流通股本（流通股本\=无限售条件流通股本+有限售条件流通股本）|股||
|ttl\_shr\_unl|float|无限售条件流通股本(行情软件定义的流通股)|股||
|ttl\_shr\_ltd|float|有限售条件股本|股||
|a\_shr\_unl|float|无限售条件流通股本(A股)|股||
|h\_shr\_unl|float|无限售条件流通股本(H股)|股||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-balance-%E6%9F%A5%E8%AF%A2%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_balance`​ - 查询资产负债表数据

查询指定时间段某一股票所属上市公司的资产负债表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_balance(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[资产负债表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#balance)**|

**示例：**

```python
stk_get_fundamentals_balance(symbol='SHSE.600000', rpt_type=12, data_type=None, start_date='2022-12-31', end_date='2022-12-31', fields='lt_eqy_inv', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type    lt_eqy_inv
0  SHSE.600000  2022-10-29  2021-12-31        12        102 2819000000.00
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近报告日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**资产负债表**

|字段名|类型|中文名称|量纲|说明|
| ---------------------------------------| -------| ------------------------------------------------------------| ------| ------------------|
|***流动资产(资产)***|||||
|cash\_bal\_cb|float|现金及存放中央银行款项|元|银行|
|dpst\_ob|float|存放同业款项|元|银行|
|mny\_cptl|float|货币资金|元||
|cust\_cred\_dpst|float|客户信用资金存款|元|证券|
|cust\_dpst|float|客户资金存款|元|证券|
|pm|float|贵金属|元|银行|
|bal\_clr|float|结算备付金|元||
|cust\_rsv|float|客户备付金|元|证券|
|ln\_to\_ob|float|拆出资金|元||
|fair\_val\_fin\_ast|float|以公允价值计量且其变动计入当期损益的金融资产|元||
|ppay|float|预付款项|元||
|fin\_out|float|融出资金|元||
|trd\_fin\_ast|float|交易性金融资产|元||
|deriv\_fin\_ast|float|衍生金融资产|元||
|note\_acct\_rcv|float|应收票据及应收账款|元||
|note\_rcv|float|应收票据|元||
|acct\_rcv|float|应收账款|元||
|acct\_rcv\_fin|float|应收款项融资|元||
|int\_rcv|float|应收利息|元||
|dvd\_rcv|float|应收股利|元||
|oth\_rcv|float|其他应收款|元||
|in\_prem\_rcv|float|应收保费|元||
|rin\_acct\_rcv|float|应收分保账款|元||
|rin\_rsv\_rcv|float|应收分保合同准备金|元|保险|
|rcv\_un\_prem\_rin\_rsv|float|应收分保未到期责任准备金|元||
|rcv\_clm\_rin\_rsv|float|应收分保未决赔偿准备金|元|保险|
|rcv\_li\_rin\_rsv|float|应收分保寿险责任准备金|元|保险|
|rcv\_lt\_hi\_rin\_rsv|float|应收分保长期健康险责任准备金|元|保险|
|ph\_plge\_ln|float|保户质押贷款|元|保险|
|ttl\_oth\_rcv|float|其他应收款合计|元||
|rfd\_dpst|float|存出保证金|元|证券、保险|
|term\_dpst|float|定期存款|元|保险|
|pur\_resell\_fin|float|买入返售金融资产|元||
|aval\_sale\_fin|float|可供出售金融资产|元||
|htm\_inv|float|持有至到期投资|元||
|hold\_for\_sale|float|持有待售资产|元||
|acct\_rcv\_inv|float|应收款项类投资|元|保险|
|invt|float|存货|元||
|contr\_ast|float|合同资产|元||
|ncur\_ast\_one\_y|float|一年内到期的非流动资产|元||
|oth\_cur\_ast|float|其他流动资产|元||
|cur\_ast\_oth\_item|float|流动资产其他项目|元||
|ttl\_cur\_ast|float|流动资产合计|元||
|***非流动资产(资产)***|||||
|loan\_adv|float|发放委托贷款及垫款|元||
|cred\_inv|float|债权投资|元||
|oth\_cred\_inv|float|其他债权投资|元||
|lt\_rcv|float|长期应收款|元||
|lt\_eqy\_inv|float|长期股权投资|元||
|oth\_eqy\_inv|float|其他权益工具投资|元||
|rfd\_cap\_guar\_dpst|float|存出资本保证金|元|保险|
|oth\_ncur\_fin\_ast|float|其他非流动金融资产|元||
|amor\_cos\_fin\_ast\_ncur|float|以摊余成本计量的金融资产（非流动）|元||
|fair\_val\_oth\_inc\_ncur|float|以公允价值计量且其变动计入其他综合收益的金融资产（非流动）|元||
|inv\_prop|float|投资性房地产|元||
|fix\_ast|float|固定资产|元||
|const\_prog|float|在建工程|元||
|const\_matl|float|工程物资|元||
|fix\_ast\_dlpl|float|固定资产清理|元||
|cptl\_bio\_ast|float|生产性生物资产|元||
|oil\_gas\_ast|float|油气资产|元||
|rig\_ast|float|使用权资产|元||
|intg\_ast|float|无形资产|元||
|trd\_seat\_fee|float|交易席位费|元|证券|
|dev\_exp|float|开发支出|元||
|gw|float|商誉|元||
|lt\_ppay\_exp|float|长期待摊费用|元||
|dfr\_tax\_ast|float|递延所得税资产|元||
|oth\_ncur\_ast|float|其他非流动资产|元||
|ncur\_ast\_oth\_item|float|非流动资产其他项目|元||
|ttl\_ncur\_ast|float|非流动资产合计|元||
|oth\_ast|float|其他资产|元|银行、证券、保险|
|ast\_oth\_item|float|资产其他项目|元||
|ind\_acct\_ast|float|独立账户资产|元|保险|
|ttl\_ast|float|资产总计|元||
|***流动负债(负债)***|||||
|brw\_cb|float|向中央银行借款|元||
|dpst\_ob\_fin\_inst|float|同业和其他金融机构存放款项|元|银行、保险|
|ln\_fm\_ob|float|拆入资金|元||
|fair\_val\_fin\_liab|float|以公允价值计量且其变动计入当期损益的金融负债|元||
|sht\_ln|float|短期借款|元||
|adv\_acct|float|预收款项|元||
|contr\_liab|float|合同负债|元||
|trd\_fin\_liab|float|交易性金融负债|元||
|deriv\_fin\_liab|float|衍生金融负债|元||
|sell\_repo\_ast|float|卖出回购金融资产款|元||
|cust\_bnk\_dpst|float|吸收存款|元|银行、保险|
|dpst\_cb\_note\_pay|float|存款证及应付票据|元|银行|
|dpst\_cb|float|存款证|元|银行|
|acct\_rcv\_adv|float|预收账款|元|保险|
|in\_prem\_rcv\_adv|float|预收保费|元|保险|
|fee\_pay|float|应付手续费及佣金|元||
|note\_acct\_pay|float|应付票据及应付账款|元||
|stlf\_pay|float|应付短期融资款|元||
|note\_pay|float|应付票据|元||
|acct\_pay|float|应付账款|元||
|rin\_acct\_pay|float|应付分保账款|元||
|emp\_comp\_pay|float|应付职工薪酬|元||
|tax\_pay|float|应交税费|元||
|int\_pay|float|应付利息|元||
|dvd\_pay|float|应付股利|元||
|ph\_dvd\_pay|float|应付保单红利|元|保险|
|indem\_pay|float|应付赔付款|元|保险|
|oth\_pay|float|其他应付款|元||
|ttl\_oth\_pay|float|其他应付款合计|元||
|ph\_dpst\_inv|float|保户储金及投资款|元|保险|
|in\_contr\_rsv|float|保险合同准备金|元|保险|
|un\_prem\_rsv|float|未到期责任准备金|元|保险|
|clm\_rin\_rsv|float|未决赔款准备金|元|保险|
|li\_liab\_rsv|float|寿险责任准备金|元|保险|
|lt\_hi\_liab\_rsv|float|长期健康险责任准备金|元|保险|
|cust\_bnk\_dpst\_fin|float|吸收存款及同业存放|元||
|inter\_pay|float|内部应付款|元||
|agy\_secu\_trd|float|代理买卖证券款|元||
|agy\_secu\_uw|float|代理承销证券款|元||
|sht\_bnd\_pay|float|应付短期债券|元||
|est\_cur\_liab|float|预计流动负债|元||
|liab\_hold\_for\_sale|float|持有待售负债|元||
|ncur\_liab\_one\_y|float|一年内到期的非流动负债|元||
|oth\_cur\_liab|float|其他流动负债|元||
|cur\_liab\_oth\_item|float|流动负债其他项目|元||
|ttl\_cur\_liab|float|流动负债合计|元||
|***非流动负债（负债）***|||||
|lt\_ln|float|长期借款|元||
|lt\_pay|float|长期应付款|元||
|leas\_liab|float|租赁负债|||
|dfr\_inc|float|递延收益|元||
|dfr\_tax\_liab|float|递延所得税负债|元||
|bnd\_pay|float|应付债券|元||
|bnd\_pay\_pbd|float|其中:永续债|元||
|bnd\_pay\_pfd|float|其中:优先股|元||
|oth\_ncur\_liab|float|其他非流动负债|元||
|spcl\_pay|float|专项应付款|元||
|ncur\_liab\_oth\_item|float|非流动负债其他项目|元||
|lt\_emp\_comp\_pay|float|长期应付职工薪酬|元||
|est\_liab|float|预计负债|元||
|oth\_liab|float|其他负债|元|银行、证券、保险|
|liab\_oth\_item|float|负债其他项目|元|银行、证券、保险|
|ttl\_ncur\_liab|float|非流动负债合计|元||
|ind\_acct\_liab|float|独立账户负债|元|保险|
|ttl\_liab|float|负债合计|元||
|***所有者权益(或股东权益)***|||||
|paid\_in\_cptl|float|实收资本（或股本）|元||
|oth\_eqy|float|其他权益工具|元||
|oth\_eqy\_pfd|float|其中:优先股|元||
|oth\_eqy\_pbd|float|其中:永续债|元||
|oth\_eqy\_oth|float|其中:其他权益工具|元||
|cptl\_rsv|float|资本公积|元||
|treas\_shr|float|库存股|元||
|oth\_comp\_inc|float|其他综合收益|元||
|spcl\_rsv|float|专项储备|元||
|sur\_rsv|float|盈余公积|元||
|rsv\_ord\_rsk|float|一般风险准备|元||
|trd\_risk\_rsv|float|交易风险准备|元|证券|
|ret\_prof|float|未分配利润|元||
|sugg\_dvd|float|建议分派股利|元|银行|
|eqy\_pcom\_oth\_item|float|归属于母公司股东权益其他项目|元||
|ttl\_eqy\_pcom|float|归属于母公司股东权益合计|元||
|min\_sheqy|float|少数股东权益|元||
|sheqy\_oth\_item|float|股东权益其他项目|元||
|ttl\_eqy|float|股东权益合计|元||
|ttl\_liab\_eqy|float|负债和股东权益合计|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-cashflow-%E6%9F%A5%E8%AF%A2%E7%8E%B0%E9%87%91%E6%B5%81%E9%87%8F%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_cashflow`​ - 查询现金流量表数据

查询指定时间段某一股票所属上市公司的现金流量表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_cashflow(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[现金流量表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#cashflow)**|

**示例：**

```python
stk_get_fundamentals_cashflow(symbol='SHSE.600000', rpt_type=None, data_type=101, start_date='2022-12-31', end_date='2022-12-31', fields='cash_pay_fee', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  cash_pay_fee
0  SHSE.600000  2022-10-29  2022-09-30         9        101 7261000000.00
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近报告日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**现金流量表**

|字段名|类型|中文名称|量纲|说明|
| ------------------------------------------| -------| -----------------------------------------------------| ------| ------------------|
|***一、经营活动产生的现金流量***|||||
|cash\_rcv\_sale|float|销售商品、提供劳务收到的现金|元||
|net\_incr\_cust\_dpst\_ob|float|客户存款和同业存放款项净增加额|元||
|net\_incr\_cust\_dpst|float|客户存款净增加额|元|银行|
|net\_incr\_dpst\_ob|float|同业及其他金融机构存放款项净增加额|元|银行|
|net\_incr\_brw\_cb|float|向中央银行借款净增加额|元||
|net\_incr\_ln\_fm\_oth|float|向其他金融机构拆入资金净增加额|元||
|cash\_rcv\_orig\_in|float|收到原保险合同保费取得的现金|元||
|net\_cash\_rcv\_rin\_biz|float|收到再保险业务现金净额|元||
|net\_incr\_ph\_dpst\_inv|float|保户储金及投资款净增加额|元||
|net\_decrdpst\_cb\_ob|float|存放中央银行和同业款项及其他金融机构净减少额|元|银行、保险|
|net\_decr\_cb|float|存放中央银行款项净减少额|元|银行|
|net\_decr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净减少额|元|银行|
|net\_cert\_dpst|float|存款证净额|元|银行|
|net\_decr\_trd\_fin|float|交易性金融资产净减少额|元|银行|
|net\_incr\_trd\_liab|float|交易性金融负债净增加额|元|银行|
|cash\_rcv\_int\_fee|float|收取利息、手续费及佣金的现金|元||
|cash\_rcv\_int|float|其中：收取利息的现金|元|银行|
|cash\_rcv\_fee|float|收取手续费及佣金的现金|元|银行|
|net\_incr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净增加额|元|银行|
|net\_incr\_ln\_fm|float|拆入资金净增加额|元||
|net\_incr\_sell\_repo|float|卖出回购金融资产款净增加额|元|银行 保险|
|net\_decr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净减少额|元|银行|
|net\_decr\_ln\_cptl|float|拆出资金净减少额|元|银行、保险|
|net\_dect\_pur\_resell|float|买入返售金融资产净减少额|元|银行、保险|
|net\_incr\_repo|float|回购业务资金净增加额|元||
|net\_decr\_repo|float|回购业务资金净减少额|元|证券|
|tax\_rbt\_rcv|float|收到的税费返还|元||
|net\_cash\_rcv\_trd|float|收到交易性金融资产现金净额|元|保险|
|cash\_rcv\_oth\_oper|float|收到其他与经营活动有关的现金|元||
|net\_cash\_agy\_secu\_trd|float|代理买卖证券收到的现金净额|元|证券|
|cash\_rcv\_pur\_resell|float|买入返售金融资产收到的现金|元|证券、保险|
|net\_cash\_agy\_secu\_uw|float|代理承销证券收到的现金净额|元|证券|
|cash\_rcv\_dspl\_debt|float|处置抵债资产收到的现金|元|银行|
|canc\_loan\_rcv|float|收回的已于以前年度核销的贷款|元|银行|
|cf\_in\_oper|float|经营活动现金流入小计|元||
|cash\_pur\_gds\_svc|float|购买商品、接受劳务支付的现金|元||
|net\_incr\_ln\_adv\_cust|float|客户贷款及垫款净增加额|元||
|net\_decr\_brw\_cb|float|向中央银行借款净减少额|元|银行|
|net\_incr\_dpst\_cb\_ob|float|存放中央银行和同业款项净增加额|元||
|net\_incr\_cb|float|存放中央银行款项净增加额|元|银行|
|net\_incr\_ob\_fin\_inst|float|存放同业及其他金融机构款项净增加额|元|银行|
|net\_decr\_dpst\_ob|float|同业及其他机构存放款减少净额|元|银行|
|net\_decr\_issu\_cert\_dpst|float|已发行存款证净减少额|元|银行|
|net\_incr\_lnto\_pur\_resell|float|拆出资金及买入返售金融资产净增加额|元|银行|
|net\_incr\_ln\_to|float|拆出资金净增加额|元|银行、保险|
|net\_incr\_pur\_resell|float|买入返售金融资产净增加额|元|银行、保险|
|net\_decr\_lnfm\_sell\_repo|float|拆入资金及卖出回购金融资产款净减少额|元|银行|
|net\_decr\_ln\_fm|float|拆入资金净减少额|元|银行、证券|
|net\_decr\_sell\_repo|float|卖出回购金融资产净减少额|元|银行、保险|
|net\_incr\_trd\_fin|float|交易性金融资产净增加额|元|银行|
|net\_decr\_trd\_liab|float|交易性金融负债净减少额|元|银行|
|cash\_pay\_indem\_orig|float|支付原保险合同赔付款项的现金|元||
|net\_cash\_pay\_rin\_biz|float|支付再保险业务现金净额|元|保险|
|cash\_pay\_int\_fee|float|支付利息、手续费及佣金的现金|元||
|cash\_pay\_int|float|其中：支付利息的现金|元|银行|
|cash\_pay\_fee|float|支付手续费及佣金的现金|元|银行|
|ph\_dvd\_pay|float|支付保单红利的现金|元||
|net\_decr\_ph\_dpst\_inv|float|保户储金及投资款净减少额|元|保险|
|cash\_pay\_emp|float|支付给职工以及为职工支付的现金|||
|cash\_pay\_tax|float|支付的各项税费|元||
|net\_cash\_pay\_trd|float|支付交易性金融资产现金净额|元|保险|
|cash\_pay\_oth\_oper|float|支付其他与经营活动有关的现金|元||
|net\_incr\_dspl\_trd\_fin|float|处置交易性金融资产净增加额|元||
|cash\_pay\_fin\_leas|float|购买融资租赁资产支付的现金|元|银行|
|net\_decr\_agy\_secu\_pay|float|代理买卖证券支付的现金净额（净减少额）|元|证券|
|net\_decr\_dspl\_trd\_fin|float|处置交易性金融资产的净减少额|元|证券|
|cf\_out\_oper|float|经营活动现金流出小计|元||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|***二、投资活动产生的现金流量：***|||||
|cash\_rcv\_sale\_inv|float|收回投资收到的现金|元||
|inv\_inc\_rcv|float|取得投资收益收到的现金|元||
|cash\_rcv\_dvd\_prof|float|分得股利或利润所收到的现金|元|银行|
|cash\_rcv\_dspl\_ast|float|处置固定资产、无形资产和其他长期资产收回的现金净额|元||
|cash\_rcv\_dspl\_sub\_oth|float|处置子公司及其他营业单位收到的现金净额|元||
|cash\_rcv\_oth\_inv|float|收到其他与投资活动有关的现金|元||
|cf\_in\_inv|float|投资活动现金流入小计|元||
|pur\_fix\_intg\_ast|float|购建固定资产、无形资产和其他长期资产支付的现金|元||
|cash\_out\_dspl\_sub\_oth|float|处置子公司及其他营业单位流出的现金净额|元|保险|
|cash\_pay\_inv|float|投资支付的现金|元||
|net\_incr\_ph\_plge\_ln|float|保户质押贷款净增加额|元|保险|
|add\_cash\_pled\_dpst|float|增加质押和定期存款所支付的现金|元||
|net\_incr\_plge\_ln|float|质押贷款净增加额|元||
|net\_cash\_get\_sub|float|取得子公司及其他营业单位支付的现金净额|元||
|net\_pay\_pur\_resell|float|支付买入返售金融资产现金净额|元|证券、保险|
|cash\_pay\_oth\_inv|float|支付其他与投资活动有关的现金|元||
|cf\_out\_inv|float|投资活动现金流出小计|||
|net\_cf\_inv|float|投资活动产生的现金流量净额|元||
|***三、筹资活动产生的现金流量：***|||||
|cash\_rcv\_cptl|float|吸收投资收到的现金|元||
|sub\_rcv\_ms\_inv|float|其中：子公司吸收少数股东投资收到的现金|元||
|brw\_rcv|float|取得借款收到的现金|元||
|cash\_rcv\_bnd\_iss|float|发行债券收到的现金|元||
|net\_cash\_rcv\_sell\_repo|float|收到卖出回购金融资产款现金净额|元|保险|
|cash\_rcv\_oth\_fin|float|收到其他与筹资活动有关的现金|元||
|issu\_cert\_dpst|float|发行存款证|元|银行|
|cf\_in\_fin\_oth|float|筹资活动现金流入其他项目|元||
|cf\_in\_fin|float|筹资活动现金流入小计|元||
|cash\_rpay\_brw|float|偿还债务支付的现金|元||
|cash\_pay\_bnd\_int|float|偿付债券利息支付的现金|元|银行|
|cash\_pay\_dvd\_int|float|分配股利、利润或偿付利息支付的现金|元||
|sub\_pay\_dvd\_prof|float|其中：子公司支付给少数股东的股利、利润|元||
|cash\_pay\_oth\_fin|float|支付其他与筹资活动有关的现金|元||
|net\_cash\_pay\_sell\_repo|float|支付卖出回购金融资产款现金净额|元|保险|
|cf\_out\_fin|float|筹资活动现金流出小计|元||
|net\_cf\_fin|float|筹资活动产生的现金流量净额|元||
|efct\_er\_chg\_cash|float|四、汇率变动对现金及现金等价物的影响|元||
|net\_incr\_cash\_eq|float|五、现金及现金等价物净增加额|元||
|cash\_cash\_eq\_bgn|float|加：期初现金及现金等价物余额|元||
|cash\_cash\_eq\_end|float|六、期末现金及现金等价物余额|元||
|***补充资料 1．将净利润调节为经营活动现金流量：***|||||
|net\_prof|float|净利润|元||
|ast\_impr|float|资产减值准备|元||
|accr\_prvs\_ln\_impa|float|计提贷款减值准备|元|银行|
|accr\_prvs\_oth\_impa|float|计提其他资产减值准备|元|银行|
|accr\_prem\_rsv|float|提取的保险责任准备金|元|保险|
|accr\_unearn\_prem\_rsv|float|提取的未到期的责任准备金|元|保险|
|defr\_fix\_prop|float|固定资产和投资性房地产折旧|元||
|depr\_oga\_cba|float|其中:固定资产折旧、油气资产折耗、生产性生物资产折旧|元||
|amor\_intg\_ast\_lt\_exp|float|无形资产及长期待摊费用等摊销|元|银行、证券、保险|
|amort\_intg\_ast|float|无形资产摊销|元||
|amort\_lt\_exp\_ppay|float|长期待摊费用摊销|元||
|dspl\_ast\_loss|float|处置固定资产、无形资产和其他长期资产的损失|元||
|fair\_val\_chg\_loss|float|固定资产报废损失|元||
|fv\_chg\_loss|float|公允价值变动损失|元||
|dfa|float|固定资产折旧|元|银行|
|fin\_exp|float|财务费用|元||
|inv\_loss|float|投资损失|元||
|exchg\_loss|float|汇兑损失|元|银行、证券、保险|
|dest\_incr|float|存款的增加|元|银行|
|loan\_decr|float|贷款的减少|元|银行|
|cash\_pay\_bnd\_int\_iss|float|发行债券利息支出|元|银行|
|dfr\_tax|float|递延所得税|元||
|dfr\_tax\_ast\_decr|float|其中:递延所得税资产减少|元||
|dfr\_tax\_liab\_incr|float|递延所得税负债增加|元||
|invt\_decr|float|存货的减少|元||
|decr\_rcv\_oper|float|经营性应收项目的减少|元||
|incr\_pay\_oper|float|经营性应付项目的增加|元||
|oth|float|其他|元||
|cash\_end|float|现金的期末余额|元||
|cash\_bgn|float|减：现金的期初余额|元||
|cash\_eq\_end|float|加:现金等价物的期末余额|元||
|cash\_eq\_bgn|float|减:现金等价物的期初余额|元||
|cred\_impr\_loss|float|信用减值损失|元||
|est\_liab\_add|float|预计负债的增加|元||
|dr\_cnv\_cptl|float|债务转为资本|元||
|cptl\_bnd\_expr\_one\_y|float|一年内到期的可转换公司债券|元||
|fin\_ls\_fix\_ast|float|融资租入固定资产|元||
|amort\_dfr\_inc|float|递延收益摊销|元||
|depr\_inv\_prop|float|投资性房地产折旧|元||
|trd\_fin\_decr|float|交易性金融资产的减少|元|证券、保险|
|im\_net\_cf\_oper|float|间接法-经营活动产生的现金流量净额|元||
|im\_net\_incr\_cash\_eq|float|间接法-现金及现金等价物净增加额|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-fundamentals-income-%E6%9F%A5%E8%AF%A2%E5%88%A9%E6%B6%A6%E8%A1%A8%E6%95%B0%E6%8D%AE)​`stk_get_fundamentals_income`​ - 查询利润表数据

查询指定时间段某一股票所属上市公司的利润表数据

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_fundamentals_income(symbol, rpt_type=None, data_type=None, start_date=None, end_date=None, fields, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型：<br />1-一季度报<br />6-中报<br />9-前三季报<br />12-年报<br />默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[利润表](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#income)**|

**示例：**

```python
stk_get_fundamentals_income(symbol='SHSE.600000', rpt_type=6, data_type=None, start_date='2022-12-31', end_date='2022-12-31', fields='inc_oper', df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type       inc_oper
0  SHSE.600000  2022-08-27  2022-06-30         6        102 98644000000.00
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近报告日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**利润表**

|字段名|类型|中文名称|量纲|说明|
| ----------------------------------------| -------| --------------------------------------| ------| ------------------|
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|net\_inc\_int|float|利息净收入|元|证券、银行、保险|
|exp\_int|float|利息支出|元||
|net\_inc\_fee\_comm|float|手续费及佣金净收入|元|证券、银行|
|inc\_rin\_prem|float|其中：分保费收入|元|保险|
|net\_inc\_secu\_agy|float|其中:代理买卖证券业务净收入|元|证券|
|inc\_fee\_comm|float|手续费及佣金收入|元||
|in\_prem\_earn|float|已赚保费|元|保险|
|inc\_in\_biz|float|其中:保险业务收入|元|保险|
|rin\_prem\_cede|float|分出保费|元|保险|
|unear\_prem\_rsv|float|提取未到期责任准备金|元|保险|
|net\_inc\_uw|float|证券承销业务净收入|元|证券|
|net\_inc\_cust\_ast\_mgmt|float|受托客户资产管理业务净收入|元|证券|
|inc\_fx|float|汇兑收益|元||
|inc\_other\_oper|float|其他业务收入|元||
|inc\_oper\_balance|float|营业收入平衡项目|元||
|ttl\_inc\_oper\_other|float|营业总收入其他项目|元||
|ttl\_cost\_oper|float|营业总成本|元||
|cost\_oper|float|营业成本|元||
|exp\_oper|float|营业支出|元|证券、银行、保险|
|biz\_tax\_sur|float|营业税金及附加|元||
|exp\_sell|float|销售费用|元||
|exp\_adm|float|管理费用|元||
|exp\_rd|float|研发费用|元||
|exp\_fin|float|财务费用|元||
|int\_fee|float|其中:利息费用|元||
|inc\_int|float|利息收入|元||
|exp\_oper\_adm|float|业务及管理费|元|证券、银行、保险|
|exp\_rin|float|减:摊回分保费用|元|保险|
|rfd\_prem|float|退保金|元|保险|
|comp\_pay|float|赔付支出|元|保险|
|rin\_clm\_pay|float|减:摊回赔付支出|元|保险|
|draw\_insur\_liab|float|提取保险责任准备金|元|保险|
|amor\_insur\_liab|float|减:摊回保险责任准备金|元|保险|
|exp\_ph\_dvd|float|保单红利支出|元|保险|
|exp\_fee\_comm|float|手续费及佣金支出|元||
|other\_oper\_cost|float|其他业务成本|元||
|oper\_exp\_balance|float|营业支出平衡项目|元|证券、银行、保险|
|exp\_oper\_other|float|营业支出其他项目|元|证券、银行、保险|
|ttl\_cost\_oper\_other|float|营业总成本其他项目|元||
|***其他经营收益***|||元||
|inc\_inv|float|投资收益|元||
|inv\_inv\_jv\_p|float|对联营企业和合营企业的投资收益|元||
|inc\_ast\_dspl|float|资产处置收益|元||
|ast\_impr\_loss|float|资产减值损失(新)|元||
|cred\_impr\_loss|float|信用减值损失(新)|元||
|inc\_fv\_chg|float|公允价值变动收益|元||
|inc\_other|float|其他收益|元||
|oper\_prof\_balance|float|营业利润平衡项目|元||
|oper\_prof|float|营业利润|元||
|inc\_noper|float|营业外收入|元||
|exp\_noper|float|营业外支出|元||
|ttl\_prof\_balance|float|利润总额平衡项目|元||
|oper\_prof\_other|float|营业利润其他项目|元||
|ttl\_prof|float|利润总额|元||
|inc\_tax|float|所得税费用|元||
|net\_prof|float|净利润|元||
|oper\_net\_prof|float|持续经营净利润|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|min\_int\_inc|float|少数股东损益|元||
|end\_net\_prof|float|终止经营净利润|元||
|net\_prof\_other|float|净利润其他项目|元||
|eps\_base|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|other\_comp\_inc|float|其他综合收益|元||
|other\_comp\_inc\_pcom|float|归属于母公司股东的其他综合收益|元||
|other\_comp\_inc\_min|float|归属于少数股东的其他综合收益|元||
|ttl\_comp\_inc|float|综合收益总额|元||
|ttl\_comp\_inc\_pcom|float|归属于母公司所有者的综合收益总额|元||
|ttl\_comp\_inc\_min|float|归属于少数股东的综合收益总额|元||
|prof\_pre\_merge|float|被合并方在合并前实现利润|元||
|net\_rsv\_in\_contr|float|提取保险合同准备金净额|元||
|net\_pay\_comp|float|赔付支出净额|元||
|net\_loss\_ncur\_ast|float|非流动资产处置净损失|元||
|amod\_fin\_asst\_end|float|以摊余成本计量的金融资产终止确认收益|元||
|cash\_flow\_hedging\_pl|float|现金流量套期损益的有效部分|元||
|cur\_trans\_diff|float|外币财务报表折算差额|元||
|gain\_ncur\_ast|float|非流动资产处置利得|元||
|afs\_fv\_chg\_pl|float|可供出售金融资产公允价值变动损益|元||
|oth\_eqy\_inv\_fv\_chg|float|其他权益工具投资公允价值变动|元||
|oth\_debt\_inv\_fv\_chg|float|其他债权投资公允价值变动|元||
|oth\_debt\_inv\_cred\_impr|float|其他债权投资信用减值准备|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-prime-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E4%B8%BB%E8%A6%81%E6%8C%87%E6%A0%87%E6%95%B0%E6%8D%AE)​`stk_get_finance_prime`​ - 查询财务主要指标数据

查询指定时间段股票所属上市公司的财务主要指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_prime(symbol, fields, rpt_type=None, data_type=None, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务主要指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。<br />101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[财务主要指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#main_financial)**|

**示例：**

```python
stk_get_finance_prime(symbol='SHSE.600000', fields='eps_basic,eps_dil',rpt_type=None, data_type=None,
start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  rpt_type  data_type  eps_dil  eps_basic
0  SHSE.600000  2023-04-29  2023-03-31         1        101     0.47       0.51
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近报告日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**财务主要指标**

|字段名|类型|中文名称|量纲|说明|
| --------------------------------| -------| --------------------------------------------| ------| ------|
|eps\_basic|float|基本每股收益|元||
|eps\_dil|float|稀释每股收益|元||
|eps\_basic\_cut|float|扣除非经常性损益后的基本每股收益|元||
|eps\_dil\_cut|float|扣除非经常性损益后的稀释每股收益|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|bps\_pcom\_ps|float|归属于母公司股东的每股净资产|元||
|ttl\_ast|float|总资产|元||
|ttl\_liab|float|总负债|元||
|share\_cptl|float|股本|股||
|ttl\_inc\_oper|float|营业总收入|元||
|inc\_oper|float|营业收入|元||
|oper\_prof|float|营业利润|元||
|ttl\_prof|float|利润总额|元||
|ttl\_eqy\_pcom|float|归属于母公司股东的所有者权益|元||
|net\_prof\_pcom|float|归属于母公司股东的净利润|元||
|net\_prof\_pcom\_cut|float|扣除非经常性损益后归属于母公司股东的净利润|元||
|roe|float|全面摊薄净资产收益率|%||
|roe\_weight\_avg|float|加权平均净资产收益率|%||
|roe\_cut|float|扣除非经常性损益后的全面摊薄净资产收益率|%||
|roe\_weight\_avg\_cut|float|扣除非经常性损益后的加权平均净资产收益率|%||
|net\_cf\_oper|float|经营活动产生的现金流量净额|元||
|eps\_yoy|float|每股收益同比比例|%||
|inc\_oper\_yoy|float|营业收入同比比例|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比比例|%||
|net\_prof\_pcom\_yoy|float|归母净利润同比比例|%||
|bps\_sh|float|归属于普通股东的每股净资产|元||
|net\_asset|float|归属于普通股东的净资产|元||
|net\_prof|float|归属于普通股东的净利润|元||
|net\_prof\_cut|float|扣除非经常性损益后归属于普通股股东的净利润|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-deriv-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E8%A1%8D%E7%94%9F%E6%8C%87%E6%A0%87%E6%95%B0%E6%8D%AE)​`stk_get_finance_deriv`​ - 查询财务衍生指标数据

查询指定时间段股票所属上市公司的财务衍生指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_deriv(symbol, fields, rpt_type=None, data_type=None, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认`None`​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。 101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认`None`​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------------| -------------| --------------| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[财务衍生指标指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#financial_derivative)**|

**示例：**

```python
stk_get_finance_deriv(symbol='SHSE.600000', fields='eps_basic,eps_dil2,eps_dil,eps_basic_cut', 
rpt_type=9, data_type=None, start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  ...  eps_dil  eps_basic  eps_dil2
0  SHSE.600000  2022-10-29  2022-09-30  ...   1.3785       1.31       1.2
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近报告日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**财务衍生指标指标**

|字段名|类型|中文名称|量纲|说明|
| ------------------------------------------------| -------| ----------------------------------------------------| ------| ------|
|eps\_basic|float|每股收益EPS(基本)|元||
|eps\_dil2|float|每股收益EPS(稀释)|元||
|eps\_dil|float|每股收益EPS(期末股本摊薄)|元||
|eps\_basic\_cut|float|每股收益EPS(扣除/基本)|元||
|eps\_dil2\_cut|float|每股收益EPS(扣除/稀释)|元||
|eps\_dil\_cut|float|每股收益EPS(扣除/期末股本摊薄)|元||
|bps|float|每股净资产BPS|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|ttl\_inc\_oper\_ps|float|每股营业总收入|元||
|inc\_oper\_ps|float|每股营业收入|元||
|ebit\_ps|float|每股息税前利润|元||
|cptl\_rsv\_ps|float|每股资本公积|元||
|sur\_rsv\_ps|float|每股盈余公积|元||
|retain\_prof\_ps|float|每股未分配利润|元||
|retain\_inc\_ps|float|每股留存收益|元||
|net\_cf\_ps|float|每股现金流量净额|元||
|fcff\_ps|float|每股企业自由现金流量|元||
|fcfe\_ps|float|每股股东自由现金流量|元||
|ebitda\_ps|float|每股EBITDA|元||
|roe|float|净资产收益率ROE(摊薄)|%||
|roe\_weight|float|净资产收益率ROE(加权)|%||
|roe\_avg|float|净资产收益率ROE(平均)|%||
|roe\_cut|float|净资产收益率ROE(扣除/摊薄)|%||
|roe\_weight\_cut|float|净资产收益率ROE(扣除/加权)|%||
|ocf\_toi|float|经营性现金净流量/营业总收入|||
|eps\_dil\_yoy|float|稀释每股收益同比增长率|%||
|net\_cf\_oper\_ps\_yoy|float|每股经营活动中产生的现金流量净额同比增长率|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比增长率|%||
|inc\_oper\_yoy|float|营业收入同比增长率|%||
|oper\_prof\_yoy|float|营业利润同比增长率|%||
|ttl\_prof\_yoy|float|利润总额同比增长率|%||
|net\_prof\_pcom\_yoy|float|归属母公司股东的净利润同比增长率|%||
|net\_prof\_pcom\_cut\_yoy|float|归属母公司股东的净利润同比增长率(扣除非经常性损益)|%||
|net\_cf\_oper\_yoy|float|经营活动产生的现金流量净额同比增长率|%||
|roe\_yoy|float|净资产收益率同比增长率(摊薄)|%||
|net\_asset\_yoy|float|净资产同比增长率|%||
|ttl\_liab\_yoy|float|总负债同比增长率|%||
|ttl\_asset\_yoy|float|总资产同比增长率|%||
|net\_cash\_flow\_yoy|float|现金净流量同比增长率|%||
|bps\_gr\_begin\_year|float|每股净资产相对年初增长率|%||
|ttl\_asset\_gr\_begin\_year|float|资产总计相对年初增长率|%||
|ttl\_eqy\_pcom\_gr\_begin\_year|float|归属母公司的股东权益相对年初增长率|%||
|net\_debt\_eqy\_ev|float|净债务/股权价值|%||
|int\_debt\_eqy\_ev|float|带息债务/股权价值|||
|eps\_bas\_yoy|float|基本每股收益同比增长率|%||
|ebit|float|EBIT(正推法)|元||
|ebitda|float|EBITDA(正推法)|元||
|ebit\_inverse|float|EBIT(反推法)|元||
|ebitda\_inverse|float|EBITDA(反推法)|元||
|nr\_prof\_loss|float|非经常性损益|元||
|net\_prof\_cut|float|扣除非经常性损益后的净利润|元||
|gross\_prof|float|毛利润|元||
|oper\_net\_inc|float|经营活动净收益|元||
|val\_chg\_net\_inc|float|价值变动净收益|元||
|exp\_rd|float|研发费用|元||
|ttl\_inv\_cptl|float|全部投入资本|元||
|work\_cptl|float|营运资本|元||
|net\_work\_cptl|float|净营运资本|元||
|tg\_asset|float|有形资产|元||
|retain\_inc|float|留存收益|元||
|int\_debt|float|带息债务|元||
|net\_debt|float|净债务|元||
|curr\_liab\_non\_int|float|无息流动负债|元||
|ncur\_liab\_non\_int|float|无息非流动负债|元||
|fcff|float|企业自由现金流量FCFF|元||
|fcfe|float|股权自由现金流量FCFE|元||
|cur\_depr\_amort|float|当期计提折旧与摊销|元||
|eqy\_mult\_dupont|float|权益乘数(杜邦分析)|||
|net\_prof\_pcom\_np|float|归属母公司股东的净利润/净利润|%||
|net\_prof\_tp|float|净利润/利润总额|%||
|ttl\_prof\_ebit|float|利润总额/息税前利润|%||
|roe\_cut\_avg|float|净资产收益率ROE(扣除/平均)|%||
|roe\_add|float|净资产收益率ROE(增发条件)|%||
|roe\_ann|float|净资产收益率ROE(年化)|%||
|roa|float|总资产报酬率ROA|%||
|roa\_ann|float|总资产报酬率ROA(年化)|%||
|jroa|float|总资产净利率|%||
|jroa\_ann|float|总资产净利率(年化)|%||
|roic|float|投入资本回报率ROIC|%||
|sale\_npm|float|销售净利率|%||
|sale\_gpm|float|销售毛利率|%||
|sale\_cost\_rate|float|销售成本率|%||
|sale\_exp\_rate|float|销售期间费用率|%||
|net\_prof\_toi|float|净利润/营业总收入|%||
|oper\_prof\_toi|float|营业利润/营业总收入|%||
|ebit\_toi|float|息税前利润/营业总收入|%||
|ttl\_cost\_oper\_toi|float|营业总成本/营业总收入|%||
|exp\_oper\_toi|float|营业费用/营业总收入|%||
|exp\_admin\_toi|float|管理费用/营业总收入|%||
|exp\_fin\_toi|float|财务费用/营业总收入|%||
|ast\_impr\_loss\_toi|float|资产减值损失/营业总收入|%||
|ebitda\_toi|float|EBITDA/营业总收入|%||
|oper\_net\_inc\_tp|float|经营活动净收益/利润总额|%||
|val\_chg\_net\_inc\_tp|float|价值变动净收益/利润总额|%||
|net\_exp\_noper\_tp|float|营业外支出净额/利润总额|||
|inc\_tax\_tp|float|所得税/利润总额|%||
|net\_prof\_cut\_np|float|扣除非经常性损益的净利润/净利润|%||
|eqy\_mult|float|权益乘数|||
|curr\_ast\_ta|float|流动资产/总资产|%||
|ncurr\_ast\_ta|float|非流动资产/总资产|%||
|tg\_ast\_ta|float|有形资产/总资产|%||
|ttl\_eqy\_pcom\_tic|float|归属母公司股东的权益/全部投入资本|%||
|int\_debt\_tic|float|带息负债/全部投入资本|%||
|curr\_liab\_tl|float|流动负债/负债合计|%||
|ncurr\_liab\_tl|float|非流动负债/负债合计|%||
|ast\_liab\_rate|float|资产负债率|%||
|quick\_rate|float|速动比率|||
|curr\_rate|float|流动比率|||
|cons\_quick\_rate|float|保守速动比率|||
|liab\_eqy\_rate|float|产权比率|||
|ttl\_eqy\_pcom\_tl|float|归属母公司股东的权益/负债合计|||
|ttl\_eqy\_pcom\_debt|float|归属母公司股东的权益/带息债务|||
|tg\_ast\_tl|float|有形资产/负债合计|||
|tg\_ast\_int\_debt|float|有形资产/带息债务|||
|tg\_ast\_net\_debt|float|有形资产/净债务|||
|ebitda\_tl|float|息税折旧摊销前利润/负债合计|||
|net\_cf\_oper\_tl|float|经营活动产生的现金流量净额/负债合计|||
|net\_cf\_oper\_int\_debt|float|经营活动产生的现金流量净额/带息债务|||
|net\_cf\_oper\_curr\_liab|float|经营活动产生的现金流量净额/流动负债|||
|net\_cf\_oper\_net\_liab|float|经营活动产生的现金流量净额/净债务|||
|ebit\_int\_cover|float|已获利息倍数|||
|long\_liab\_work\_cptl|float|长期债务与营运资金比率|||
|ebitda\_int\_debt|float|EBITDA/带息债务|%||
|oper\_cycle|float|营业周期|天||
|inv\_turnover\_days|float|存货周转天数|天||
|acct\_rcv\_turnover\_days|float|应收账款周转天数(含应收票据)|天||
|inv\_turnover\_rate|float|存货周转率|次||
|acct\_rcv\_turnover\_rate|float|应收账款周转率(含应收票据)|次||
|curr\_ast\_turnover\_rate|float|流动资产周转率|次||
|fix\_ast\_turnover\_rate|float|固定资产周转率|次||
|ttl\_ast\_turnover\_rate|float|总资产周转率|次||
|cash\_rcv\_sale\_oi|float|销售商品提供劳务收到的现金/营业收入|%||
|net\_cf\_oper\_oi|float|经营活动产生的现金流量净额/营业收入|%||
|net\_cf\_oper\_oni|float|经营活动产生的现金流量净额/经营活动净收益|||
|cptl\_exp\_da|float|资本支出/折旧摊销|%||
|cash\_rate|float|现金比率|||
|acct\_pay\_turnover\_days|float|应付账款周转天数(含应付票据)|天||
|acct\_pay\_turnover\_rate|float|应付账款周转率(含应付票据)|次||
|net\_oper\_cycle|float|净营业周期|天||
|ttl\_cost\_oper\_yoy|float|营业总成本同比增长率|%||
|net\_prof\_yoy|float|净利润同比增长率|%||
|net\_cf\_oper\_np|float|经营活动产生的现金流量净额/净利润|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-valuation-%E6%9F%A5%E8%AF%A2%E4%BC%B0%E5%80%BC%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_valuation`​ - 查询估值指标每日数据

查询指定时间段股票的每日估值指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_valuation(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[估值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#valuation)**|

**示例：**

```python
stk_get_daily_valuation(symbol='SHSE.600000', fields='pe_ttm,pe_lyr,pe_mrq', start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  pe_ttm  pe_lyr  pe_mrq
0  SHSE.600000  2023-06-26  4.4139   4.107  3.3188
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近交易日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  如果`fields`​参数的指标字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**估值指标**

|字段名|类型|中文名称|量纲|说明|
| ----------------------| -------| --------------------------------------------| ------| ------|
|pe\_ttm|float|市盈率(TTM)|倍||
|pe\_lyr|float|市盈率(最新年报LYR)|倍||
|pe\_mrq|float|市盈率(最新报告期MRQ)|倍||
|pe\_1q|float|市盈率(当年一季×4)|倍||
|pe\_2q|float|市盈率(当年中报×2)|倍||
|pe\_3q|float|市盈率(当年三季×4/3)|倍||
|pe\_ttm\_cut|float|市盈率(TTM) 扣除非经常性损益|倍||
|pe\_lyr\_cut|float|市盈率(最新年报LYR) 扣除非经常性损益|倍||
|pe\_mrq\_cut|float|市盈率(最新报告期MRQ) 扣除非经常性损益|倍||
|pe\_1q\_cut|float|市盈率(当年一季×4) 扣除非经常性损益|倍||
|pe\_2q\_cut|float|市盈率(当年中报×2) 扣除非经常性损益|倍||
|pe\_3q\_cut|float|市盈率(当年三季×4/3) 扣除非经常性损益|倍||
|pb\_lyr|float|市净率(最新年报LYR)|倍||
|pb\_mrq|float|市净率(最新报告期MRQ)|倍||
|pb\_lyr\_1|float|市净率(剔除其他权益工具，最新年报LYR)|倍||
|pb\_mrq\_1|float|市净率(剔除其他权益工具，最新报告期MRQ)|倍||
|pcf\_ttm\_oper|float|市现率(经营现金流,TTM)|倍||
|pcf\_ttm\_ncf|float|市现率(现金净流量,TTM)|倍||
|pcf\_lyr\_oper|float|市现率(经营现金流,最新年报LYR)|倍||
|pcf\_lyr\_ncf|float|市现率(现金净流量,最新年报LYR)|倍||
|ps\_ttm|float|市销率(TTM)|倍||
|ps\_lyr|float|市销率(最新年报LYR)|倍||
|ps\_mrq|float|市销率(最新报告期MRQ)|倍||
|ps\_1q|float|市销率(当年一季×4)|倍||
|ps\_2q|float|市销率(当年中报×2)|倍||
|ps\_3q|float|市销率(当年三季×4/3)|倍||
|peg\_lyr|float|历史PEG值(当年年报增长率)|||
|peg\_mrq|float|历史PEG值(最新报告期增长率)|||
|peg\_1q|float|历史PEG值(当年1季\*4较上年年报增长率)|||
|peg\_2q|float|历史PEG值(当年中报\*2较上年年报增长率)|||
|peg\_3q|float|历史PEG值(当年3季\*4/3较上年年报增长率)|||
|peg\_np\_cgr|float|历史PEG值(PE\_TTM较净利润3年复合增长率)|||
|peg\_npp\_cgr|float|历史PEG值(PE\_TTM较净利润3年复合增长率)|||
|dy\_ttm|float|股息率(滚动 12 月TTM)|%||
|dy\_lfy|float|股息率(上一财年LFY)|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-mktvalue-%E6%9F%A5%E8%AF%A2%E5%B8%82%E5%80%BC%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_mktvalue`​ - 查询市值指标每日数据

查询指定时间段股票的每日市值指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_mktvalue(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[市值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#mktvalue)**|

**示例：**

```python
stk_get_daily_mktvalue(symbol='SHSE.600000', fields='tot_mv,tot_mv_csrc,a_mv',
                                  start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date      tot_mv  tot_mv_csrc        a_mv
0  SHSE.600000  2023-06-26  2.1016e+11   2.1016e+11  2.1016e+11
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近交易日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  如果`fields`​参数的指标字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**市值指标**

|字段名|类型|中文名称|量纲|说明|
| -------------------------| -------| -----------------------------------| ------| ------|
|tot\_mv|float|总市值|元||
|tot\_mv\_csrc|float|总市值(证监会算法)|元||
|a\_mv|float|A股流通市值(含限售股)|元||
|a\_mv\_ex\_ltd|float|A股流通市值(不含限售股)|元||
|b\_mv|float|B股流通市值(含限售股，折人民币)|元||
|b\_mv\_ex\_ltd|float|B股流通市值(不含限售股，折人民币)|元||
|ev|float|企业价值(含货币资金)(EV1)|元||
|ev\_ex\_curr|float|企业价值(剔除货币资金)(EV2)|元||
|ev\_ebitda|float|企业倍数|倍||
|equity\_value|float|股权价值|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-basic-%E6%9F%A5%E8%AF%A2%E8%82%A1%E6%9C%AC%E7%AD%89%E5%9F%BA%E7%A1%80%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_basic`​ - 查询股本等基础指标每日数据

查询指定时间段股票的每日基础指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_basic(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------------| ------| ----------| ------| --------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考[symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认`None`​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认`False`​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------------| -------------| --------------| --------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields`​字段的数值. 支持的字段名请参考 **[基础指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#daily_basic)**|

**示例：**

```python
stk_get_daily_basic(symbol='SHSE.600000', fields='tclose,turnrate,ttl_shr,circ_shr',
                                  start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  turnrate    circ_shr     ttl_shr  tclose
0  SHSE.600000  2023-06-26    0.1159  2.9352e+10  2.9352e+10    7.16
 
        复制成功
  
```

**注意：**

**1.**  当`start_date`​ \=\= `end_date`​时，取离 `end_date`​ 最近交易日期的一条数据，

当`start_dat`​\< `end_date`​时，取指定时间段的数据，

当 `start_date`​ \> `end_date`​时，返回报错。

**2.**  如果`fields`​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields不能超过20个字段

**基础指标**

|字段名|类型|中文名称|量纲|说明|
| ---------------------| -------| ----------------------------------------------------------------| ------| ------|
|tclose|float|收盘价|元||
|turnrate|float|当日换手率|%||
|ttl\_shr|float|总股本|股||
|circ\_shr|float|流通股本（流通股本\=无限售条件流通股本+有限售条件流通股本）|股||
|ttl\_shr\_unl|float|无限售条件流通股本(A股+H股)|股||
|ttl\_shr\_ltd|float|有限售条件股本|股||
|a\_shr\_unl|float|无限售条件流通A股股本(行情软件定义的流通股)|股||
|h\_shr\_unl|float|无限售条件流通H股股本|股||

---

上次更新: 11/27/20

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-finance-deriv-%E6%9F%A5%E8%AF%A2%E8%B4%A2%E5%8A%A1%E8%A1%8D%E7%94%9F%E6%8C%87%E6%A0%87%E6%95%B0%E6%8D%AE)​`stk_get_finance_deriv`​ - 查询财务衍生指标数据

查询指定时间段股票所属上市公司的财务衍生指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_finance_deriv(symbol, fields, rpt_type=None, data_type=None, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| --------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务衍生指标， 如有多个字段，中间用**英文逗号**分隔|
|rpt\_type|int|报表类型|N|None|按**报告期**查询可指定以下报表类型： 1-一季度报<br />6-中报<br />9-前三季报<br />12-年报 默认 `None` ​为不限|
|data\_type|int|数据类型|N|None|在发布原始财务报告以后，上市公司可能会对数据进行修正。 101-合并原始<br />102-合并调整<br />201-母公司原始<br />202-母公司调整 默认 `None` ​返回当期合并调整，如果没有调整返回合并原始|
|start\_date|str|开始时间|N|None|开始时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为报告日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ---------| -----------| ------------| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|symbol|str|股票代码||
|pub\_date|str|发布日期|若数据类型选择合并原始(`data_type=101`​)，则返回原始发布的发布日期 若数据类型选择合并调整(`data_type=102`​)，则返回调整后最新发布日期 若数据类型选择母公司原始(`data_type=201`​)，则返回母公司原始发布的发布日期<br />若数据类型选择母公司调整(`data_type=202`​)，则返回母公司调整后最新发布日期|
|rpt\_date|str|报告日期|报告截止日期，财报统计的最后一天，在指定时间段[开始时间,结束时间]内的报告截止日期|
|rpt\_type|int|报表类型|返回数据的报表类型：1-一季度报, 6-中报, 9-前三季报, 12-年报|
|data\_type|int|数据类型|返回数据的数据类型：101-合并原始, 102-合并调整, 201-母公司原始, 202-母公司调整|
|fields|list[float]|财务字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[财务衍生指标指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#financial_derivative)**|

**示例：**

```python
stk_get_finance_deriv(symbol='SHSE.600000', fields='eps_basic,eps_dil2,eps_dil,eps_basic_cut', 
rpt_type=9, data_type=None, start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol    pub_date    rpt_date  ...  eps_dil  eps_basic  eps_dil2
0  SHSE.600000  2022-10-29  2022-09-30  ...   1.3785       1.31       1.2
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近报告日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  若在指定历史时间段内，有多个同一类型报表（如不同年份的一季度报表），将按照报告日期顺序返回。

**3.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**财务衍生指标指标**

|字段名|类型|中文名称|量纲|说明|
| --------------------------| -----| --------------------------------------------------| ----| ----|
|eps\_basic|float|每股收益 EPS(基本)|元||
|eps\_dil2|float|每股收益 EPS(稀释)|元||
|eps\_dil|float|每股收益 EPS(期末股本摊薄)|元||
|eps\_basic\_cut|float|每股收益 EPS(扣除/基本)|元||
|eps\_dil2\_cut|float|每股收益 EPS(扣除/稀释)|元||
|eps\_dil\_cut|float|每股收益 EPS(扣除/期末股本摊薄)|元||
|bps|float|每股净资产 BPS|元||
|net\_cf\_oper\_ps|float|每股经营活动产生的现金流量净额|元||
|ttl\_inc\_oper\_ps|float|每股营业总收入|元||
|inc\_oper\_ps|float|每股营业收入|元||
|ebit\_ps|float|每股息税前利润|元||
|cptl\_rsv\_ps|float|每股资本公积|元||
|sur\_rsv\_ps|float|每股盈余公积|元||
|retain\_prof\_ps|float|每股未分配利润|元||
|retain\_inc\_ps|float|每股留存收益|元||
|net\_cf\_ps|float|每股现金流量净额|元||
|fcff\_ps|float|每股企业自由现金流量|元||
|fcfe\_ps|float|每股股东自由现金流量|元||
|ebitda\_ps|float|每股 EBITDA|元||
|roe|float|净资产收益率 ROE(摊薄)|%||
|roe\_weight|float|净资产收益率 ROE(加权)|%||
|roe\_avg|float|净资产收益率 ROE(平均)|%||
|roe\_cut|float|净资产收益率 ROE(扣除/摊薄)|%||
|roe\_weight\_cut|float|净资产收益率 ROE(扣除/加权)|%||
|ocf\_toi|float|经营性现金净流量/营业总收入|||
|eps\_dil\_yoy|float|稀释每股收益同比增长率|%||
|net\_cf\_oper\_ps\_yoy|float|每股经营活动中产生的现金流量净额同比增长率|%||
|ttl\_inc\_oper\_yoy|float|营业总收入同比增长率|%||
|inc\_oper\_yoy|float|营业收入同比增长率|%||
|oper\_prof\_yoy|float|营业利润同比增长率|%||
|ttl\_prof\_yoy|float|利润总额同比增长率|%||
|net\_prof\_pcom\_yoy|float|归属母公司股东的净利润同比增长率|%||
|net\_prof\_pcom\_cut\_yoy|float|归属母公司股东的净利润同比增长率(扣除非经常性损益)|%||
|net\_cf\_oper\_yoy|float|经营活动产生的现金流量净额同比增长率|%||
|roe\_yoy|float|净资产收益率同比增长率(摊薄)|%||
|net\_asset\_yoy|float|净资产同比增长率|%||
|ttl\_liab\_yoy|float|总负债同比增长率|%||
|ttl\_asset\_yoy|float|总资产同比增长率|%||
|net\_cash\_flow\_yoy|float|现金净流量同比增长率|%||
|bps\_gr\_begin\_year|float|每股净资产相对年初增长率|%||
|ttl\_asset\_gr\_begin\_year|float|资产总计相对年初增长率|%||
|ttl\_eqy\_pcom\_gr\_begin\_year|float|归属母公司的股东权益相对年初增长率|%||
|net\_debt\_eqy\_ev|float|净债务/股权价值|%||
|int\_debt\_eqy\_ev|float|带息债务/股权价值|||
|eps\_bas\_yoy|float|基本每股收益同比增长率|%||
|ebit|float|EBIT(正推法)|元||
|ebitda|float|EBITDA(正推法)|元||
|ebit\_inverse|float|EBIT(反推法)|元||
|ebitda\_inverse|float|EBITDA(反推法)|元||
|nr\_prof\_loss|float|非经常性损益|元||
|net\_prof\_cut|float|扣除非经常性损益后的净利润|元||
|gross\_prof|float|毛利润|元||
|oper\_net\_inc|float|经营活动净收益|元||
|val\_chg\_net\_inc|float|价值变动净收益|元||
|exp\_rd|float|研发费用|元||
|ttl\_inv\_cptl|float|全部投入资本|元||
|work\_cptl|float|营运资本|元||
|net\_work\_cptl|float|净营运资本|元||
|tg\_asset|float|有形资产|元||
|retain\_inc|float|留存收益|元||
|int\_debt|float|带息债务|元||
|net\_debt|float|净债务|元||
|curr\_liab\_non\_int|float|无息流动负债|元||
|ncur\_liab\_non\_int|float|无息非流动负债|元||
|fcff|float|企业自由现金流量 FCFF|元||
|fcfe|float|股权自由现金流量 FCFE|元||
|cur\_depr\_amort|float|当期计提折旧与摊销|元||
|eqy\_mult\_dupont|float|权益乘数(杜邦分析)|||
|net\_prof\_pcom\_np|float|归属母公司股东的净利润/净利润|%||
|net\_prof\_tp|float|净利润/利润总额|%||
|ttl\_prof\_ebit|float|利润总额/息税前利润|%||
|roe\_cut\_avg|float|净资产收益率 ROE(扣除/平均)|%||
|roe\_add|float|净资产收益率 ROE(增发条件)|%||
|roe\_ann|float|净资产收益率 ROE(年化)|%||
|roa|float|总资产报酬率 ROA|%||
|roa\_ann|float|总资产报酬率 ROA(年化)|%||
|jroa|float|总资产净利率|%||
|jroa\_ann|float|总资产净利率(年化)|%||
|roic|float|投入资本回报率 ROIC|%||
|sale\_npm|float|销售净利率|%||
|sale\_gpm|float|销售毛利率|%||
|sale\_cost\_rate|float|销售成本率|%||
|sale\_exp\_rate|float|销售期间费用率|%||
|net\_prof\_toi|float|净利润/营业总收入|%||
|oper\_prof\_toi|float|营业利润/营业总收入|%||
|ebit\_toi|float|息税前利润/营业总收入|%||
|ttl\_cost\_oper\_toi|float|营业总成本/营业总收入|%||
|exp\_oper\_toi|float|营业费用/营业总收入|%||
|exp\_admin\_toi|float|管理费用/营业总收入|%||
|exp\_fin\_toi|float|财务费用/营业总收入|%||
|ast\_impr\_loss\_toi|float|资产减值损失/营业总收入|%||
|ebitda\_toi|float|EBITDA/营业总收入|%||
|oper\_net\_inc\_tp|float|经营活动净收益/利润总额|%||
|val\_chg\_net\_inc\_tp|float|价值变动净收益/利润总额|%||
|net\_exp\_noper\_tp|float|营业外支出净额/利润总额|||
|inc\_tax\_tp|float|所得税/利润总额|%||
|net\_prof\_cut\_np|float|扣除非经常性损益的净利润/净利润|%||
|eqy\_mult|float|权益乘数|||
|curr\_ast\_ta|float|流动资产/总资产|%||
|ncurr\_ast\_ta|float|非流动资产/总资产|%||
|tg\_ast\_ta|float|有形资产/总资产|%||
|ttl\_eqy\_pcom\_tic|float|归属母公司股东的权益/全部投入资本|%||
|int\_debt\_tic|float|带息负债/全部投入资本|%||
|curr\_liab\_tl|float|流动负债/负债合计|%||
|ncurr\_liab\_tl|float|非流动负债/负债合计|%||
|ast\_liab\_rate|float|资产负债率|%||
|quick\_rate|float|速动比率|||
|curr\_rate|float|流动比率|||
|cons\_quick\_rate|float|保守速动比率|||
|liab\_eqy\_rate|float|产权比率|||
|ttl\_eqy\_pcom\_tl|float|归属母公司股东的权益/负债合计|||
|ttl\_eqy\_pcom\_debt|float|归属母公司股东的权益/带息债务|||
|tg\_ast\_tl|float|有形资产/负债合计|||
|tg\_ast\_int\_debt|float|有形资产/带息债务|||
|tg\_ast\_net\_debt|float|有形资产/净债务|||
|ebitda\_tl|float|息税折旧摊销前利润/负债合计|||
|net\_cf\_oper\_tl|float|经营活动产生的现金流量净额/负债合计|||
|net\_cf\_oper\_int\_debt|float|经营活动产生的现金流量净额/带息债务|||
|net\_cf\_oper\_curr\_liab|float|经营活动产生的现金流量净额/流动负债|||
|net\_cf\_oper\_net\_liab|float|经营活动产生的现金流量净额/净债务|||
|ebit\_int\_cover|float|已获利息倍数|||
|long\_liab\_work\_cptl|float|长期债务与营运资金比率|||
|ebitda\_int\_debt|float|EBITDA/带息债务|%||
|oper\_cycle|float|营业周期|天||
|inv\_turnover\_days|float|存货周转天数|天||
|acct\_rcv\_turnover\_days|float|应收账款周转天数(含应收票据)|天||
|inv\_turnover\_rate|float|存货周转率|次||
|acct\_rcv\_turnover\_rate|float|应收账款周转率(含应收票据)|次||
|curr\_ast\_turnover\_rate|float|流动资产周转率|次||
|fix\_ast\_turnover\_rate|float|固定资产周转率|次||
|ttl\_ast\_turnover\_rate|float|总资产周转率|次||
|cash\_rcv\_sale\_oi|float|销售商品提供劳务收到的现金/营业收入|%||
|net\_cf\_oper\_oi|float|经营活动产生的现金流量净额/营业收入|%||
|net\_cf\_oper\_oni|float|经营活动产生的现金流量净额/经营活动净收益|||
|cptl\_exp\_da|float|资本支出/折旧摊销|%||
|cash\_rate|float|现金比率|||
|acct\_pay\_turnover\_days|float|应付账款周转天数(含应付票据)|天||
|acct\_pay\_turnover\_rate|float|应付账款周转率(含应付票据)|次||
|net\_oper\_cycle|float|净营业周期|天||
|ttl\_cost\_oper\_yoy|float|营业总成本同比增长率|%||
|net\_prof\_yoy|float|净利润同比增长率|%||
|net\_cf\_oper\_np|float|经营活动产生的现金流量净额/净利润|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-valuation-%E6%9F%A5%E8%AF%A2%E4%BC%B0%E5%80%BC%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_valuation`​ - 查询估值指标每日数据

查询指定时间段股票的每日估值指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_valuation(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[估值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#valuation)**|

**示例：**

```python
stk_get_daily_valuation(symbol='SHSE.600000', fields='pe_ttm,pe_lyr,pe_mrq', start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  pe_ttm  pe_lyr  pe_mrq
0  SHSE.600000  2023-06-26  4.4139   4.107  3.3188
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近交易日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  如果 `fields` ​参数的指标字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**估值指标**

|字段名|类型|中文名称|量纲|说明|
| ------------| -----| -------------------------------------------| ----| ----|
|pe\_ttm|float|市盈率(TTM)|倍||
|pe\_lyr|float|市盈率(最新年报 LYR)|倍||
|pe\_mrq|float|市盈率(最新报告期 MRQ)|倍||
|pe\_1q|float|市盈率(当年一季 ×4)|倍||
|pe\_2q|float|市盈率(当年中报 ×2)|倍||
|pe\_3q|float|市盈率(当年三季 ×4/3)|倍||
|pe\_ttm\_cut|float|市盈率(TTM) 扣除非经常性损益|倍||
|pe\_lyr\_cut|float|市盈率(最新年报 LYR) 扣除非经常性损益|倍||
|pe\_mrq\_cut|float|市盈率(最新报告期 MRQ) 扣除非经常性损益|倍||
|pe\_1q\_cut|float|市盈率(当年一季 ×4) 扣除非经常性损益|倍||
|pe\_2q\_cut|float|市盈率(当年中报 ×2) 扣除非经常性损益|倍||
|pe\_3q\_cut|float|市盈率(当年三季 ×4/3) 扣除非经常性损益|倍||
|pb\_lyr|float|市净率(最新年报 LYR)|倍||
|pb\_mrq|float|市净率(最新报告期 MRQ)|倍||
|pb\_lyr\_1|float|市净率(剔除其他权益工具，最新年报 LYR)|倍||
|pb\_mrq\_1|float|市净率(剔除其他权益工具，最新报告期 MRQ)|倍||
|pcf\_ttm\_oper|float|市现率(经营现金流,TTM)|倍||
|pcf\_ttm\_ncf|float|市现率(现金净流量,TTM)|倍||
|pcf\_lyr\_oper|float|市现率(经营现金流,最新年报 LYR)|倍||
|pcf\_lyr\_ncf|float|市现率(现金净流量,最新年报 LYR)|倍||
|ps\_ttm|float|市销率(TTM)|倍||
|ps\_lyr|float|市销率(最新年报 LYR)|倍||
|ps\_mrq|float|市销率(最新报告期 MRQ)|倍||
|ps\_1q|float|市销率(当年一季 ×4)|倍||
|ps\_2q|float|市销率(当年中报 ×2)|倍||
|ps\_3q|float|市销率(当年三季 ×4/3)|倍||
|peg\_lyr|float|历史 PEG 值(当年年报增长率)|||
|peg\_mrq|float|历史 PEG 值(最新报告期增长率)|||
|peg\_1q|float|历史 PEG 值(当年 1 季\*4 较上年年报增长率)|||
|peg\_2q|float|历史 PEG 值(当年中报\*2 较上年年报增长率)|||
|peg\_3q|float|历史 PEG 值(当年 3 季\*4/3 较上年年报增长率)|||
|peg\_np\_cgr|float|历史 PEG 值(PE\_TTM 较净利润 3 年复合增长率)|||
|peg\_npp\_cgr|float|历史 PEG 值(PE\_TTM 较净利润 3 年复合增长率)|||
|dy\_ttm|float|股息率(滚动 12 月 TTM)|%||
|dy\_lfy|float|股息率(上一财年 LFY)|%||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-mktvalue-%E6%9F%A5%E8%AF%A2%E5%B8%82%E5%80%BC%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_mktvalue`​ - 查询市值指标每日数据

查询指定时间段股票的每日市值指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_mktvalue(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[市值指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#mktvalue)**|

**示例：**

```python
stk_get_daily_mktvalue(symbol='SHSE.600000', fields='tot_mv,tot_mv_csrc,a_mv',
                                  start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date      tot_mv  tot_mv_csrc        a_mv
0  SHSE.600000  2023-06-26  2.1016e+11   2.1016e+11  2.1016e+11
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近交易日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  如果 `fields` ​参数的指标字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**市值指标**

|字段名|类型|中文名称|量纲|说明|
| ------------| -----| ----------------------------------| ----| ----|
|tot\_mv|float|总市值|元||
|tot\_mv\_csrc|float|总市值(证监会算法)|元||
|a\_mv|float|A 股流通市值(含限售股)|元||
|a\_mv\_ex\_ltd|float|A 股流通市值(不含限售股)|元||
|b\_mv|float|B 股流通市值(含限售股，折人民币)|元||
|b\_mv\_ex\_ltd|float|B 股流通市值(不含限售股，折人民币)|元||
|ev|float|企业价值(含货币资金)(EV1)|元||
|ev\_ex\_curr|float|企业价值(剔除货币资金)(EV2)|元||
|ev\_ebitda|float|企业倍数|倍||
|equity\_value|float|股权价值|元||

## [#](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#stk-get-daily-basic-%E6%9F%A5%E8%AF%A2%E8%82%A1%E6%9C%AC%E7%AD%89%E5%9F%BA%E7%A1%80%E6%8C%87%E6%A0%87%E6%AF%8F%E6%97%A5%E6%95%B0%E6%8D%AE)​`stk_get_daily_basic`​ - 查询股本等基础指标每日数据

查询指定时间段股票的每日基础指标

**此函数为掘金公版(体验版/专业版/机构版)函数，券商版以升级提示为准**

**函数原型：**

```python
stk_get_daily_basic(symbol, fields, start_date=None, end_date=None, df=False)
 
        复制成功
  
```

**参数：**

|参数名|类型|中文名称|必填|默认值|参数用法说明|
| ----------| ----| --------| ----| ------| ------------------------------------------------------------------|
|symbol|str|股票代码|Y|无|必填，只能填一个股票标的，使用时参考 [symbol](https://www.myquant.cn/docs2/sdk/python/%E5%8F%98%E9%87%8F%E7%BA%A6%E5%AE%9A.html#symbol-%E4%BB%A3%E7%A0%81%E6%A0%87%E8%AF%86)|
|fields|str|返回字段|Y|无|指定需要返回的财务字段， 如有多个字段，中间用**英文逗号**分隔|
|start\_date|str|开始时间|N|None|开始时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|end\_date|str|结束时间|N|None|结束时间，时间类型为交易日期，%Y-%m-%d 格式， 默认 `None` ​表示最新时间|
|df|bool|返回格式|N|False|是否返回 dataframe 格式 ， 默认 `False` ​返回 list[dict]|

**返回值：**

|**字段名**|**类型**|**中文名称**|**说明**|
| ----------| -----------| ------------| -------------------------------------------|
|symbol|str|股票代码||
|trade\_date|str|交易日期||
|fields|list[float]|指标字段数据|指定返回 `fields` ​字段的数值. 支持的字段名请参考 **[基础指标](https://www.myquant.cn/docs2/sdk/python/API%E4%BB%8B%E7%BB%8D/%E8%82%A1%E7%A5%A8%E8%B4%A2%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%8F%8A%E5%9F%BA%E7%A1%80%E6%95%B0%E6%8D%AE%E5%87%BD%E6%95%B0%EF%BC%88%E5%85%8D%E8%B4%B9%EF%BC%89.html#daily_basic)**|

**示例：**

```python
stk_get_daily_basic(symbol='SHSE.600000', fields='tclose,turnrate,ttl_shr,circ_shr',
                                  start_date=None, end_date=None, df=True)
 
        复制成功
  
```

**输出：**

```python
        symbol  trade_date  turnrate    circ_shr     ttl_shr  tclose
0  SHSE.600000  2023-06-26    0.1159  2.9352e+10  2.9352e+10    7.16
 
        复制成功
  
```

**注意：**

**1.**  当 `start_date`​ \=\= `end_date` ​时，取离 `end_date`​ 最近交易日期的一条数据，

当 `start_dat`​\< `end_date` ​时，取指定时间段的数据，

当 `start_date`​ \> `end_date` ​时，返回报错。

**2.**  如果 `fields` ​参数的财务字段填写不正确，或填写空字段，会报错提示“填写的 fields 不正确”。fields 不能超过 20 个字段

**基础指标**

|字段名|类型|中文名称|量纲|说明|
| -----------| -----| ------------------------------------------------------------| ----| ----|
|tclose|float|收盘价|元||
|turnrate|float|当日换手率|%||
|ttl\_shr|float|总股本|股||
|circ\_shr|float|流通股本（流通股本\=无限售条件流通股本 + 有限售条件流通股本）|股||
|ttl\_shr\_unl|float|无限售条件流通股本(A 股 +H 股)|股||
|ttl\_shr\_ltd|float|有限售条件股本|股||
|a\_shr\_unl|float|无限售条件流通 A 股股本(行情软件定义的流通股)|股||
|h\_shr\_unl|float|无限售条件流通 H 股股本|股||

---

