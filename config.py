"""
回测系统配置文件
"""

# 数据库连接配置
DATABASE_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'root',            # 数据库用户名
    'password': '6547899x',    # 数据库密码，请修改为实际密码
    'database': 'stock_goldminer_bj',  # 数据库名称
    'charset': 'utf8mb4'       # 字符集
}

# 回测策略参数
STRATEGY_CONFIG = {
    'initial_capital': 1000000,    # 初始资金（元）
    'rebalance_freq': 20,          # 调仓频率（交易日）
    'max_positions': 10,           # 最大持仓数量
    'pe_min': 0,                   # 市盈率最小值
    'pe_max': 40,                  # 市盈率最大值
    'cash_reserve_ratio': 0.02     # 现金保留比例（2%）
}

# 回测时间配置
TIME_CONFIG = {
    'start_date': '2025-04-01',    # 回测开始日期
    'end_date': None               # 回测结束日期，None表示到今天
}

# 输出配置
OUTPUT_CONFIG = {
    'save_csv': True,              # 是否保存CSV文件
    'csv_filename': 'backtest_results.csv',  # CSV文件名
    'show_progress': True,         # 是否显示进度
    'progress_interval': 10        # 进度显示间隔（天）
}
