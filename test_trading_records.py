"""
测试交易记录功能
生成模拟数据来验证交易记录的正确性
"""

import os
import csv
from datetime import datetime, timedelta

def generate_mock_trading_records():
    """生成模拟交易记录用于测试"""
    records = []
    
    # 模拟第一次调仓 - 买入
    base_date = '2025-04-01'
    stocks = ['SH600001', 'SH600002', 'SH600003', 'SH600004', 'SH600005']
    
    for i, symbol in enumerate(stocks):
        price = 10.0 + i * 0.5
        shares = 1000 - i * 100
        records.append({
            'date': base_date,
            'action': '买入',
            'symbol': symbol,
            'shares': shares,
            'price': price,
            'value': shares * price,
            'buy_price': price,
            'profit_loss': 0,
            'profit_loss_pct': 0
        })
    
    # 模拟第二次调仓 - 卖出和买入
    base_date = '2025-04-21'
    
    # 卖出前面的股票
    for i, symbol in enumerate(stocks):
        buy_price = 10.0 + i * 0.5
        sell_price = buy_price * (1 + (i - 2) * 0.05)  # 有涨有跌
        shares = 1000 - i * 100
        profit_loss = (sell_price - buy_price) * shares
        profit_loss_pct = (sell_price - buy_price) / buy_price * 100
        
        records.append({
            'date': base_date,
            'action': '卖出',
            'symbol': symbol,
            'shares': shares,
            'price': sell_price,
            'value': shares * sell_price,
            'buy_price': buy_price,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct
        })
    
    # 买入新股票
    new_stocks = ['SH600006', 'SH600007', 'SH600008', 'SH600009', 'SH600010']
    for i, symbol in enumerate(new_stocks):
        price = 8.0 + i * 0.3
        shares = 1200 - i * 150
        records.append({
            'date': base_date,
            'action': '买入',
            'symbol': symbol,
            'shares': shares,
            'price': price,
            'value': shares * price,
            'buy_price': price,
            'profit_loss': 0,
            'profit_loss_pct': 0
        })
    
    return records

def save_test_trading_records(records, filename='test_trading_records.csv'):
    """保存测试交易记录"""
    with open(filename, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        # 写入表头
        writer.writerow(['date', 'action', 'symbol', 'shares', 'price', 'value', 
                        'buy_price', 'profit_loss', 'profit_loss_pct'])
        
        # 写入数据
        for record in records:
            writer.writerow([
                record['date'],
                record['action'],
                record['symbol'],
                record['shares'],
                f"{record['price']:.4f}",
                f"{record['value']:.2f}",
                f"{record['buy_price']:.4f}",
                f"{record['profit_loss']:.2f}",
                f"{record['profit_loss_pct']:.2f}"
            ])

def analyze_test_records(records):
    """分析测试记录"""
    print("=" * 60)
    print("测试交易记录分析")
    print("=" * 60)
    
    buy_records = [r for r in records if r['action'] == '买入']
    sell_records = [r for r in records if r['action'] == '卖出']
    
    print(f"总交易次数: {len(records)}")
    print(f"买入次数: {len(buy_records)}")
    print(f"卖出次数: {len(sell_records)}")
    
    if sell_records:
        total_profit_loss = sum(r['profit_loss'] for r in sell_records)
        profitable_trades = [r for r in sell_records if r['profit_loss'] > 0]
        losing_trades = [r for r in sell_records if r['profit_loss'] < 0]
        
        print(f"\n盈亏统计:")
        print(f"总盈亏: ¥{total_profit_loss:,.2f}")
        print(f"盈利交易: {len(profitable_trades)} 次")
        print(f"亏损交易: {len(losing_trades)} 次")
        
        if len(sell_records) > 0:
            win_rate = len(profitable_trades) / len(sell_records) * 100
            print(f"胜率: {win_rate:.2f}%")
        
        print(f"\n详细交易记录:")
        print("日期\t\t动作\t股票代码\t股数\t价格\t\t盈亏")
        print("-" * 70)
        for record in records:
            if record['action'] == '卖出':
                print(f"{record['date']}\t{record['action']}\t{record['symbol']}\t"
                      f"{record['shares']}\t{record['price']:.2f}\t\t"
                      f"{record['profit_loss']:+.2f} ({record['profit_loss_pct']:+.2f}%)")
            else:
                print(f"{record['date']}\t{record['action']}\t{record['symbol']}\t"
                      f"{record['shares']}\t{record['price']:.2f}\t\t--")

def test_csv_files_exist():
    """测试CSV文件是否存在"""
    expected_files = [
        'backtest_results.csv',
        'trading_records.csv', 
        'positions_summary.csv'
    ]
    
    print("\n" + "=" * 60)
    print("检查输出文件")
    print("=" * 60)
    
    for filename in expected_files:
        if os.path.exists(filename):
            # 获取文件大小
            size = os.path.getsize(filename)
            print(f"✓ {filename} 存在 ({size} 字节)")
            
            # 读取前几行
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:3]  # 读取前3行
                    print(f"  前3行预览:")
                    for i, line in enumerate(lines):
                        print(f"    {i+1}: {line.strip()}")
            except Exception as e:
                print(f"  读取文件失败: {e}")
        else:
            print(f"✗ {filename} 不存在")

def main():
    """主函数"""
    print("=" * 60)
    print("交易记录功能测试")
    print("=" * 60)
    
    # 生成测试数据
    print("生成测试交易记录...")
    records = generate_mock_trading_records()
    
    # 保存测试数据
    save_test_trading_records(records)
    print("✓ 测试交易记录已保存到 test_trading_records.csv")
    
    # 分析测试数据
    analyze_test_records(records)
    
    # 检查实际回测生成的文件
    test_csv_files_exist()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("使用说明:")
    print("1. 运行 python basic_backtest.py 执行回测")
    print("2. 检查生成的3个CSV文件:")
    print("   - backtest_results.csv: 组合价值记录")
    print("   - trading_records.csv: 详细交易记录") 
    print("   - positions_summary.csv: 持仓汇总")
    print("3. 使用Excel或其他工具分析交易数据")

if __name__ == "__main__":
    main()
